/**
 * Slug utilities for building SEO-friendly content URLs
 */

/**
 * Convert a title to a URL-safe slug.
 * - Lowercase
 * - Remove non alphanumeric/space/hyphen
 * - Collapse whitespace to single hyphens
 * - Collapse multiple hyphens
 * - Trim leading/trailing hyphens
 */
export function slugifyTitle(title: string): string {
  if (!title) return '';
  return title
    .toLowerCase()
    .normalize('NFKD') // normalize accents
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Build the content path segment: `${slug}-${id}` ensuring the id is preserved
 * so backend can still fetch using the ID regardless of slug changes.
 */
export function buildContentPath(title: string, id: string): string {
  const slug = slugifyTitle(title);
  if (!id) return slug || '';
  if (slug) return `${slug}-${id}`;
  return id;
}

/**
 * Extract the canonical content ID from a route param which may be either:
 * - The raw id (e.g., `content_123...`)
 * - A slug followed by `-` and the id (e.g., `pretty-thing-hin-eng-content_123...`)
 */
export function extractIdFromParam(param?: string | null): string | null {
  if (!param) return null;
  // If the param already looks like an id, return as-is
  if (param.startsWith('content_')) return param;
  // Find the last occurrence of the id marker
  const idx = param.lastIndexOf('content_');
  if (idx >= 0) {
    return param.substring(idx);
  }
  return null;
}

