/**
 * SEO Utilities for StreamDB
 * Handles dynamic sitemap generation and SEO optimization
 */

export interface SitemapUrl {
  loc: string;
  lastmod: string;
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: string;
  comment?: string;
}

export interface ContentItem {
  id: string;
  title: string;
  type: 'movie' | 'series';
  slug?: string;
  updatedAt?: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  updatedAt?: string;
}

/**
 * Generate sitemap URLs for static pages
 */
export function getStaticSitemapUrls(): SitemapUrl[] {
  const baseUrl = 'https://streamdb.online';
  const currentDate = new Date().toISOString().split('T')[0];

  return [
    {
      loc: baseUrl,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: '1.0',
      comment: 'Homepage - Latest Movies & Series'
    },
    {
      loc: `${baseUrl}/movies`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: '0.9',
      comment: 'All Movies Collection'
    },
    {
      loc: `${baseUrl}/series`,
      lastmod: currentDate,
      changefreq: 'daily',
      priority: '0.9',
      comment: 'All TV Series Collection'
    },
    {
      loc: `${baseUrl}/requested`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '0.7',
      comment: 'User Requested Content'
    },
    {
      loc: `${baseUrl}/categories`,
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '0.8',
      comment: 'Content Categories'
    },
    {
      loc: `${baseUrl}/disclaimer`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: '0.3',
      comment: 'Legal Disclaimer'
    },
    {
      loc: `${baseUrl}/dmca`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: '0.3',
      comment: 'DMCA Policy'
    },
    {
      loc: `${baseUrl}/contact`,
      lastmod: currentDate,
      changefreq: 'monthly',
      priority: '0.5',
      comment: 'Contact Information'
    }
  ];
}

/**
 * Generate sitemap URLs for content items (movies/series)
 */
import { buildContentPath } from '@/utils/slug';

export function getContentSitemapUrls(content: ContentItem[]): SitemapUrl[] {
  const baseUrl = 'https://streamdb.online';

  return content.map(item => ({
    loc: `${baseUrl}/content/${buildContentPath(item.title, item.id)}`,
    lastmod: item.updatedAt ? item.updatedAt.split('T')[0] : new Date().toISOString().split('T')[0],
    changefreq: 'weekly' as const,
    priority: '0.6',
    comment: `${item.type === 'movie' ? 'Movie' : 'Series'}: ${item.title}`
  }));
}

/**
 * Generate sitemap URLs for categories
 */
export function getCategorySitemapUrls(categories: Category[]): SitemapUrl[] {
  const baseUrl = 'https://streamdb.online';
  
  return categories.map(category => ({
    loc: `${baseUrl}/category/${category.slug}`,
    lastmod: category.updatedAt ? category.updatedAt.split('T')[0] : new Date().toISOString().split('T')[0],
    changefreq: 'weekly' as const,
    priority: '0.7',
    comment: `Category: ${category.name}`
  }));
}

/**
 * Generate complete sitemap XML content
 */
export function generateSitemapXml(urls: SitemapUrl[]): string {
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
  
  urls.forEach(url => {
    if (url.comment) {
      xml += `  <!-- ${url.comment} -->\n`;
    }
    xml += '  <url>\n';
    xml += `    <loc>${url.loc}</loc>\n`;
    xml += `    <lastmod>${url.lastmod}</lastmod>\n`;
    xml += `    <changefreq>${url.changefreq}</changefreq>\n`;
    xml += `    <priority>${url.priority}</priority>\n`;
    xml += '  </url>\n';
  });
  
  xml += '</urlset>';
  return xml;
}

/**
 * Check if a URL should be excluded from sitemap (admin routes, etc.)
 */
export function shouldExcludeFromSitemap(url: string): boolean {
  const excludedPatterns = [
    '/admin',
    '/login',
    '/reset-password',
    '/_admin',
    '/api/admin',
    '/dashboard',
    '/management'
  ];
  
  return excludedPatterns.some(pattern => url.includes(pattern));
}

/**
 * Generate meta tags for SEO
 */
export function generateMetaTags(options: {
  title: string;
  description: string;
  url?: string;
  image?: string;
  type?: 'website' | 'article' | 'video.movie' | 'video.tv_show';
  keywords?: string[];
}): string {
  const baseUrl = 'https://streamdb.online';
  const defaultImage = `${baseUrl}/android-chrome-512x512.png`;
  
  const {
    title,
    description,
    url = baseUrl,
    image = defaultImage,
    type = 'website',
    keywords = []
  } = options;

  return `
    <title>${title}</title>
    <meta name="description" content="${description}" />
    <meta name="keywords" content="${keywords.join(', ')}" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="${type}" />
    <meta property="og:url" content="${url}" />
    <meta property="og:title" content="${title}" />
    <meta property="og:description" content="${description}" />
    <meta property="og:image" content="${image}" />
    <meta property="og:site_name" content="StreamDB" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="${url}" />
    <meta property="twitter:title" content="${title}" />
    <meta property="twitter:description" content="${description}" />
    <meta property="twitter:image" content="${image}" />
    
    <!-- Additional SEO -->
    <meta name="robots" content="index, follow" />
    <meta name="googlebot" content="index, follow" />
    <link rel="canonical" href="${url}" />
  `.trim();
}

/**
 * Generate structured data (JSON-LD) for content
 */
export function generateStructuredData(content: {
  type: 'movie' | 'series' | 'website';
  title: string;
  description: string;
  url: string;
  image?: string;
  datePublished?: string;
  genre?: string[];
  director?: string[];
  actor?: string[];
  rating?: number;
}): string {
  const baseStructure = {
    "@context": "https://schema.org",
    "@type": content.type === 'movie' ? 'Movie' : content.type === 'series' ? 'TVSeries' : 'WebSite',
    "name": content.title,
    "description": content.description,
    "url": content.url,
    "image": content.image || 'https://streamdb.online/android-chrome-512x512.png'
  };

  if (content.type === 'movie' || content.type === 'series') {
    Object.assign(baseStructure, {
      "datePublished": content.datePublished,
      "genre": content.genre,
      "director": content.director?.map(name => ({ "@type": "Person", "name": name })),
      "actor": content.actor?.map(name => ({ "@type": "Person", "name": name })),
      "aggregateRating": content.rating ? {
        "@type": "AggregateRating",
        "ratingValue": content.rating,
        "bestRating": 10
      } : undefined
    });
  }

  return JSON.stringify(baseStructure, null, 2);
}