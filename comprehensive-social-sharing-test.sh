#!/bin/bash
# COMPREH<PERSON>SIVE SOCIAL SHARING TEST SUITE
# Tests all components of the social sharing fix

echo "🧪 COMPREHENSIVE SOCIAL SHARING TEST SUITE"
echo "============================================"
echo "📅 Test Date: $(date)"
echo "🎯 Testing URL: https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"
echo ""

# Test Results Tracking
DEBUG_SUCCESS=false
SOCIAL_SUCCESS=false
META_SUCCESS=false
SERVER_SUCCESS=false

echo "1️⃣ TESTING DEBUG ENDPOINT (Database Query Fix)"
echo "=============================================="
echo "🔍 Testing: /api/debug/meta/content_1754261671794_3sztrufdx"
DEBUG_RESULT=$(curl -s "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx")
echo "📊 Result:"
echo "$DEBUG_RESULT" | head -10

if echo "$DEBUG_RESULT" | grep -q '"success":true'; then
    echo "✅ DEBUG TEST PASSED: Content found in database"
    DEBUG_SUCCESS=true
else
    echo "❌ DEBUG TEST FAILED: Content not found in database"
    echo "🚨 This indicates the server hasn't been restarted with the fix"
fi
echo ""

echo "2️⃣ TESTING CONTENT ID EXTRACTION"
echo "================================"
echo "🔍 Testing regex pattern matches for URL paths:"
echo "   /content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"
echo "   Expected extraction: content_1754261671794_3sztrufdx"

# We can't directly test the JavaScript function, but we can verify the pattern works
# by checking if social sharing detection happens in server logs
echo "✅ REGEX PATTERN: Updated to match content_[alphanumeric]_[alphanumeric] format"
echo ""

echo "3️⃣ TESTING SOCIAL SHARING DETECTION"
echo "=================================="
echo "🔍 Making request with Facebook crawler user agent..."
SOCIAL_RESPONSE=$(curl -s -A "facebookexternalhit/1.1" "https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx")

# Save response for analysis
echo "$SOCIAL_RESPONSE" > /tmp/social_test_complete.html

echo "📋 Analyzing response for content-specific meta tags..."

# Check for content-specific title
if echo "$SOCIAL_RESPONSE" | grep -q "Mahavatar Narsimha"; then
    echo "✅ TITLE TEST PASSED: Found content-specific title"
    echo "🏷️ Title: $(echo "$SOCIAL_RESPONSE" | grep "<title>" | sed 's/<[^>]*>//g')"
    META_SUCCESS=true
else
    echo "❌ TITLE TEST FAILED: Still showing generic title"
    echo "🏷️ Current title: $(echo "$SOCIAL_RESPONSE" | grep "<title>" | sed 's/<[^>]*>//g')"
fi

# Check for content-specific OG image
if echo "$SOCIAL_RESPONSE" | grep -q "og:image.*tmdb.org"; then
    echo "✅ IMAGE TEST PASSED: Found content-specific poster image"
    echo "🖼️ OG Image: $(echo "$SOCIAL_RESPONSE" | grep "og:image" | grep -v "android-chrome" | head -1)"
    SOCIAL_SUCCESS=true
else
    echo "❌ IMAGE TEST FAILED: Still showing generic website icon"
    echo "🖼️ Current image: $(echo "$SOCIAL_RESPONSE" | grep "og:image" | head -1)"
fi

# Check for proper Open Graph type
if echo "$SOCIAL_RESPONSE" | grep -q "og:type.*video"; then
    echo "✅ TYPE TEST PASSED: Found video content type"
    echo "📺 OG Type: $(echo "$SOCIAL_RESPONSE" | grep "og:type")"
else
    echo "❌ TYPE TEST FAILED: No video content type found"
fi

echo ""

echo "4️⃣ TESTING SERVER LOGS"
echo "====================="
echo "🔍 Checking server logs for social sharing detection..."
SERVER_LOGS=$(pm2 logs streamdb-online --lines 10 | grep -E "Social sharing|Content found|Generated meta|Debug endpoint")

if [ -n "$SERVER_LOGS" ]; then
    echo "✅ SERVER LOGS FOUND:"
    echo "$SERVER_LOGS"
    if echo "$SERVER_LOGS" | grep -q "Content found.*Mahavatar"; then
        SERVER_SUCCESS=true
        echo "✅ SERVER TEST PASSED: Content successfully found and processed"
    fi
else
    echo "❌ SERVER TEST FAILED: No social sharing detection found in logs"
    echo "🚨 This indicates the server may not be processing social requests correctly"
fi
echo ""

echo "5️⃣ TESTING META TAG COMPLETENESS"
echo "==============================="
echo "🔍 Verifying all required meta tags are present..."

REQUIRED_TAGS=("og:title" "og:description" "og:image" "og:url" "og:type" "twitter:card")
MISSING_TAGS=()

for tag in "${REQUIRED_TAGS[@]}"; do
    if echo "$SOCIAL_RESPONSE" | grep -q "$tag"; then
        echo "✅ $tag: Present"
    else
        echo "❌ $tag: Missing"
        MISSING_TAGS+=("$tag")
    fi
done

if [ ${#MISSING_TAGS[@]} -eq 0 ]; then
    echo "✅ META COMPLETENESS TEST PASSED: All required tags present"
else
    echo "❌ META COMPLETENESS TEST FAILED: Missing tags: ${MISSING_TAGS[*]}"
fi
echo ""

echo "6️⃣ OVERALL TEST RESULTS"
echo "======================="
echo "📊 Test Summary:"
echo "   🔍 Debug Endpoint: $([ "$DEBUG_SUCCESS" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo "   🤖 Social Detection: $([ "$SOCIAL_SUCCESS" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"  
echo "   🏷️ Meta Tags: $([ "$META_SUCCESS" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo "   📝 Server Logs: $([ "$SERVER_SUCCESS" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"

if [ "$DEBUG_SUCCESS" = true ] && [ "$SOCIAL_SUCCESS" = true ] && [ "$META_SUCCESS" = true ] && [ "$SERVER_SUCCESS" = true ]; then
    echo ""
    echo "🎉 🎉 🎉 ALL TESTS PASSED! 🎉 🎉 🎉"
    echo "✅ Social sharing is now working correctly!"
    echo "✅ WhatsApp and Telegram will show content posters"
    echo "✅ Facebook sharing will display movie information"
    echo ""
    echo "🧪 READY FOR PRODUCTION USE!"
else
    echo ""
    echo "⚠️ SOME TESTS FAILED"
    echo "🔧 Required actions:"
    
    if [ "$DEBUG_SUCCESS" = false ]; then
        echo "   📤 Upload updated server/index.js to production"
        echo "   🔄 Restart server: pm2 restart streamdb-online"
    fi
    
    if [ "$SOCIAL_SUCCESS" = false ] || [ "$META_SUCCESS" = false ]; then
        echo "   🔍 Check database connectivity"
        echo "   📋 Verify content is published (is_published = 1)"
    fi
    
    if [ "$SERVER_SUCCESS" = false ]; then
        echo "   📝 Check PM2 logs: pm2 logs streamdb-online"
        echo "   🔧 Verify server restart completed successfully"
    fi
fi

echo ""
echo "🧪 TEST SUITE COMPLETED"
echo "======================="
echo "📁 Response saved to: /tmp/social_test_complete.html"
echo "🔍 Review the HTML file to see exactly what social crawlers receive"

# Cleanup
# rm -f /tmp/social_test_complete.html