#!/bin/bash
# PRODUCTION SOCIAL SHARING DIAGNOSTIC
# Run this script on your production server to diagnose the social sharing issue
# This is a READ-ONLY script - it makes NO changes to files or database

echo "🔧 PRODUCTION SOCIAL SHARING DIAGNOSTIC"
echo "======================================="
echo "📅 $(date)"
echo "🌐 Server: $(hostname)"
echo "👤 User: $(whoami)"
echo ""

# Test content ID for diagnosis
CONTENT_ID="content_1754261671794_3sztrufdx"
CONTENT_URL="https://streamdb.online/content/mahavatar-narsimha-hin-tel-$CONTENT_ID"

echo "🎯 TESTING CONTENT ID: $CONTENT_ID"
echo "🔗 TESTING URL: $CONTENT_URL"
echo ""

echo "1️⃣ SERVER ENVIRONMENT CHECK"
echo "==========================="
echo "📊 PM2 Status:"
pm2 list | grep streamdb-online
echo ""
echo "📊 Node.js Version: $(node --version 2>/dev/null || echo 'Node.js not found')"
echo "📊 Current Directory: $(pwd)"
echo "📊 Server Files:"
ls -la /var/www/streamdb_root/data/www/streamdb.online/server/index.js 2>/dev/null || echo "❌ index.js not found"
echo ""

echo "2️⃣ DATABASE CONNECTION TEST"
echo "==========================="
echo "📊 Testing database connection and content existence..."
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "
SELECT 'Database connection: SUCCESS' as status;
SELECT COUNT(*) as total_content FROM content;
SELECT COUNT(*) as published_content FROM content WHERE is_published = 1;
SELECT id, title, type, year, is_published 
FROM content 
WHERE id = '$CONTENT_ID' OR title LIKE '%Mahavatar%'
LIMIT 5;
" 2>/dev/null || echo "❌ Database connection failed or credentials not set"
echo ""

echo "3️⃣ DEBUG ENDPOINT TEST"
echo "======================"
echo "📊 Testing /api/debug/meta endpoint..."
DEBUG_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" "https://streamdb.online/api/debug/meta/$CONTENT_ID" 2>/dev/null)
HTTP_CODE=$(echo "$DEBUG_RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$DEBUG_RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

echo "📡 HTTP Status: $HTTP_CODE"
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Debug endpoint working"
    echo "📄 Response: $RESPONSE_BODY" | head -c 300
    if echo "$RESPONSE_BODY" | grep -q "Mahavatar Narsimha"; then
        echo "✅ Content found in debug endpoint"
    else
        echo "❌ Content NOT found in debug endpoint"
    fi
else
    echo "❌ Debug endpoint failed with status $HTTP_CODE"
    echo "📄 Response: $RESPONSE_BODY"
fi
echo ""

echo "4️⃣ SOCIAL SHARING TEST (CRITICAL)"
echo "================================="
echo "📊 Testing with Facebook crawler user agent..."
SOCIAL_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" -A "facebookexternalhit/1.1" "$CONTENT_URL" 2>/dev/null)
SOCIAL_HTTP_CODE=$(echo "$SOCIAL_RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
SOCIAL_BODY=$(echo "$SOCIAL_RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

echo "📡 Social HTTP Status: $SOCIAL_HTTP_CODE"

if [ "$SOCIAL_HTTP_CODE" = "200" ]; then
    echo "✅ Social request successful"
    
    # Save response for detailed analysis
    echo "$SOCIAL_BODY" > /tmp/social_response.html
    
    echo ""
    echo "📊 ANALYZING SOCIAL RESPONSE:"
    echo "============================"
    
    # Check title
    if grep -q "<title>.*Mahavatar Narsimha.*</title>" /tmp/social_response.html; then
        echo "✅ SUCCESS: Content-specific title found!"
        grep -o "<title>[^<]*</title>" /tmp/social_response.html
    else
        echo "❌ FAIL: Generic title found"
        grep -o "<title>[^<]*</title>" /tmp/social_response.html || echo "No title found"
    fi
    
    # Check og:image
    echo ""
    if grep -q 'property="og:image".*tmdb\.org' /tmp/social_response.html; then
        echo "✅ SUCCESS: Content-specific image found!"
        grep -o 'property="og:image"[^>]*content="[^"]*"' /tmp/social_response.html
    else
        echo "❌ FAIL: Generic image found"
        grep -o 'property="og:image"[^>]*content="[^"]*"' /tmp/social_response.html || echo "No og:image found"
    fi
    
    # Check og:title
    echo ""
    if grep -q 'property="og:title".*Mahavatar' /tmp/social_response.html; then
        echo "✅ SUCCESS: Content-specific og:title found!"
        grep -o 'property="og:title"[^>]*content="[^"]*"' /tmp/social_response.html
    else
        echo "❌ FAIL: Generic og:title found"
        grep -o 'property="og:title"[^>]*content="[^"]*"' /tmp/social_response.html || echo "No og:title found"
    fi
    
    # Clean up
    rm -f /tmp/social_response.html
    
else
    echo "❌ Social request failed with status $SOCIAL_HTTP_CODE"
    echo "📄 Error response: $SOCIAL_BODY" | head -c 500
fi

echo ""
echo "5️⃣ SERVER LOGS ANALYSIS"
echo "======================="
echo "📊 Recent server logs (last 20 lines):"
pm2 logs streamdb-online --lines 20 --nostream 2>/dev/null | tail -20 || echo "❌ Cannot access PM2 logs"
echo ""

echo "6️⃣ ROUTE TESTING"
echo "================"
echo "📊 Testing different URL patterns..."

# Test direct content access
echo "🔍 Testing: /api/content/$CONTENT_ID"
curl -s -w "HTTP_CODE:%{http_code}\n" "https://streamdb.online/api/content/$CONTENT_ID" | tail -1

# Test health endpoint
echo "🔍 Testing: /api/health"
curl -s -w "HTTP_CODE:%{http_code}\n" "https://streamdb.online/api/health" | tail -1

echo ""
echo "7️⃣ DIAGNOSIS SUMMARY"
echo "===================="
if [ "$HTTP_CODE" = "200" ] && [ "$SOCIAL_HTTP_CODE" = "200" ]; then
    if echo "$DEBUG_RESPONSE" | grep -q "Mahavatar" && echo "$SOCIAL_BODY" | grep -q "Mahavatar"; then
        echo "🎉 SUCCESS: Both debug endpoint and social sharing are working!"
    elif echo "$DEBUG_RESPONSE" | grep -q "Mahavatar" && ! echo "$SOCIAL_BODY" | grep -q "Mahavatar"; then
        echo "🚨 ISSUE IDENTIFIED: Debug works, social sharing fails"
        echo "💡 CAUSE: Social media detection logic is not working in main handler"
        echo "🔧 SOLUTION NEEDED: Fix social media user agent detection or route handling"
    else
        echo "🚨 ISSUE: Both endpoints have problems"
    fi
else
    echo "🚨 CONNECTIVITY ISSUE: HTTP errors detected"
fi

echo ""
echo "✅ DIAGNOSTIC COMPLETED"
echo "======================"
echo "📋 Share these results to get the issue fixed!"