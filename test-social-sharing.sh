#!/bin/bash
# Test social sharing meta tags

echo "🔍 TESTING SOCIAL SHARING META TAGS"
echo "==================================="
echo "📅 $(date)"
echo ""

echo "1️⃣ TESTING WITH FACEBOOK CRAWLER USER AGENT"
echo "============================================"
echo "🔍 URL: https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"
echo ""

# Test with Facebook crawler user agent
echo "📊 Response with Facebook crawler user agent:"
curl -s -A "facebookexternalhit/1.1" "https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx" > /tmp/facebook_response.html

# Check for content-specific meta tags
echo ""
echo "🏷️ CHECKING META TAGS:"
echo "======================"

echo "📋 Page Title:"
grep "<title>" /tmp/facebook_response.html || echo "No title found"

echo ""
echo "📋 OG Title:"
grep "og:title" /tmp/facebook_response.html || echo "No OG title found"

echo ""
echo "📋 OG Image:"
grep "og:image" /tmp/facebook_response.html || echo "No OG image found"

echo ""
echo "📋 OG Description:"
grep "og:description" /tmp/facebook_response.html || echo "No OG description found"

echo ""
echo "2️⃣ CHECKING FOR CONTENT-SPECIFIC vs GENERIC"
echo "==========================================="

if grep -q "Mahavatar Narsimha" /tmp/facebook_response.html; then
    echo "✅ SUCCESS: Found content-specific title 'Mahavatar Narsimha'"
else
    echo "❌ PROBLEM: Content-specific title NOT found"
fi

if grep -q "wfGTnnP35bWCd8iOtmKa2Hy7MAQ.jpg" /tmp/facebook_response.html; then
    echo "✅ SUCCESS: Found content-specific poster image"
else
    echo "❌ PROBLEM: Content-specific poster NOT found"
fi

if grep -q "android-chrome-512x512.png" /tmp/facebook_response.html; then
    echo "❌ PROBLEM: Found generic website icon instead of poster"
else
    echo "✅ GOOD: No generic website icon found"
fi

echo ""
echo "3️⃣ FIRST 500 CHARACTERS OF RESPONSE"
echo "==================================="
head -c 500 /tmp/facebook_response.html
echo ""
echo "..."

# Cleanup
rm -f /tmp/facebook_response.html

echo ""
echo "✅ TEST COMPLETED"