# StreamDB Online - Complete Project Memory Documentation

## 🚀 Project Overview

**StreamDB Online** is a comprehensive streaming content management system featuring movies and web series with an advanced admin panel. The project uses React/TypeScript frontend, Node.js/Express backend, MySQL database, and a two-tier offshore VPS infrastructure for enhanced security and DMCA protection.

**Domain**: streamdb.online  
**Development Path**: `G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB`  
**Infrastructure**: Two-tier offshore VPS with reverse proxy architecture  

---

## 🏗️ Complete Infrastructure Architecture

### System Architecture Flow
```
User → Cloudflare → Reverse Proxy (*************) → Backend Server (***********)
                                                   ↓
                                    Nginx (80/443) + Apache (Internal)
                                                   ↓
                                    Node.js App (3001) + FastPanel2 (8888)
                                                   ↓
                                              MySQL Database (3306)
```

### Infrastructure Components

#### **Reverse Proxy Server (*************)**
- **Purpose**: SSL termination, load balancing, security filtering, DMCA protection
- **Services**: <PERSON>inx reverse proxy, Cloudflare integration
- **Security Role**: Primary shield hiding backend server IP from public discovery

#### **Backend Server (***********)**
- **OS**: Ubuntu 24.04.2 LTS (Kernel 6.8.0-63-generic)
- **Specs**: 4GB RAM, 38GB storage, QEMU Virtual CPU @ 2.0GHz
- **Application Path**: `/var/www/streamdb_root/data/www/streamdb.online/`
- **Control Panel**: FastPanel2 on port 8888

#### **Active Services**
```bash
streamdb-online (PM2)    # Node.js application (85.6MB memory)
mysql.service            # MySQL Database (297.4MB memory)
nginx.service            # Web server (public facing)
apache2.service          # FastPanel integration
fastpanel2.service       # Control panel
```

#### **Port Configuration**
- **3001**: Node.js App (IPv6 localhost only)
- **3306**: MySQL (localhost socket only)
- **80/443**: Nginx (public facing)
- **8888**: FastPanel2 HTTPS

### Security & Firewall
```bash
# UFW Rules
22/tcp     ALLOW Anywhere         # SSH
80/tcp     ALLOW Anywhere         # HTTP
443/tcp    ALLOW Anywhere         # HTTPS
8888/tcp   ALLOW Anywhere         # FastPanel2
Anywhere   ALLOW *************    # Reverse Proxy Only
```

---

## 🛠️ Technology Stack

### Frontend Technologies
- **Framework**: React 18.3.1 + TypeScript 5.5.3 + Vite 5.4.1
- **Styling**: Tailwind CSS 3.4.11 + shadcn/ui components
- **State**: React Context API + TanStack React Query 5.56.2
- **Routing**: React Router DOM 6.26.2
- **Forms**: React Hook Form 7.53.0 + Zod validation

### Backend Technologies
- **Runtime**: Node.js v20.19.3 + Express.js 4.18.2
- **Database**: MySQL 8.0.42 + mysql2 driver 3.6.5
- **Authentication**: JWT + bcrypt (12 rounds) + express-session
- **Security**: helmet, express-rate-limit, cors
- **File Processing**: multer + sharp for image optimization

### External Integrations
- **TMDB API**: Movie/TV metadata, images, search
- **OMDB API**: IMDB ratings, additional metadata
- **Email**: nodemailer for notifications

---

## 🗄️ Database Architecture

### Configuration
- **Name**: `stream_db`
- **Connection**: Socket-based (`/var/run/mysqld/mysqld.sock`)
- **Charset**: utf8mb4_unicode_ci
- **Security**: Local-only access, no external exposure

### Complete Schema (17 Tables)

#### Core Content Tables
```sql
content                    # Movies/series (9 records)
├── seasons               # Series seasons (4 records)  
└── episodes              # Individual episodes (8 records)

categories                # 18 predefined categories
content_sections          # Homepage sections (6 active)
content_section_mappings  # Section-content relationships (60 mappings)
```

#### Authentication & Security
```sql
admin_users              # Admin accounts (1 record)
admin_security_logs      # Admin activity (210 records)
security_logs            # System security (1158 records - heavily used)
sessions                 # Express sessions
auth_tokens             # JWT tokens
login_attempts          # Failed logins
password_reset_tokens   # Reset functionality
```

#### Analytics
```sql
ad_blocker_tracking     # User analytics (87 records)
```

### Key Relationships
- Content → Seasons → Episodes (hierarchical)
- Content ↔ Sections (many-to-many via mappings)
- Content → Categories (many-to-one)

---

## ⚛️ Frontend Architecture

### Component Structure
```
src/
├── components/
│   ├── admin/                    # Admin panel components
│   │   ├── AddTitleForm.tsx     # Content creation
│   │   ├── ContentManager.tsx   # Content management
│   │   ├── WebSeriesManager.tsx # Seasons/episodes
│   │   └── SectionsManager.tsx  # Homepage sections
│   ├── ui/                      # shadcn/ui components
│   ├── HeroCarousel.tsx         # Homepage carousel (max 10)
│   ├── CardGrid.tsx             # Content grid
│   ├── SecureVideoPlayer.tsx    # Protected video player
│   ├── SafeImage.tsx            # Optimized images
│   └── Header.tsx               # Navigation
├── contexts/AuthContext.tsx     # Auth state management
├── pages/                       # Route components
├── services/apiService.ts       # API communication
└── utils/                       # Utility functions
```

### Routing
```
/                     → Homepage with dynamic sections
/movies              → Movies listing
/series              → Web series listing
/category/:slug      → Category pages
/content/:id         → Individual content pages
/admin/*             → Protected admin area
/login               → Authentication
```

### Key Features
- **Dynamic Homepage**: Database-driven sections with carousel
- **Admin Panel**: Complete CRUD operations, TMDB/OMDB integration
- **Video Security**: Encrypted embed links, secure player
- **Mobile Optimization**: Responsive design, touch-friendly
- **Social Media Sharing**: Content-specific meta tags, Open Graph support

---

## 🔧 Backend API Architecture

### Server Structure
```
server/
├── index.js                 # Main entry point
├── config/database.js       # MySQL connection
├── routes/                  # API endpoints
│   ├── auth.js             # Authentication
│   ├── content.js          # Content CRUD
│   ├── admin.js            # Admin operations
│   ├── episodes.js         # Series management
│   └── sections.js         # Homepage sections
├── middleware/auth.js       # JWT middleware
└── services/               # Business logic
```

### API Endpoints Summary

#### Authentication (`/api/auth`)
- `POST /login` - JWT authentication
- `POST /logout` - Session termination
- `GET /verify` - Token validation
- `POST /change-password` - Password updates

#### Content Management (`/api/content`)
- `GET /` - Fetch with filtering/pagination/search
- `GET /:id` - Specific content with relationships
- `POST /` - Create new content (admin)
- `PUT /:id` - Update content (admin)
- `DELETE /:id` - Remove content (admin)

#### Episodes Management (`/api/episodes`)
- `GET /content/:contentId` - All seasons/episodes
- `POST /seasons` - Create season
- `POST /episodes` - Create episode
- `PUT /seasons/:id` - Update season
- `DELETE /seasons/:id` - Remove season

#### Admin Operations (`/api/admin`)
- `GET /stats` - Dashboard statistics
- `GET /security-logs` - Audit trail
- `POST /bulk-upload` - CSV import

### Security Implementation
- **JWT Authentication**: Secure token-based auth
- **Rate Limiting**: 1000 req/15min general, 500 req/15min auth
- **CSP Headers**: Monetag ads support, security protection
- **Input Validation**: Comprehensive server-side validation

---

## 📱 Social Media Sharing System

### ✅ Implementation Status: PRODUCTION-READY
The social sharing feature has been successfully implemented and validated in production. All components are functioning correctly with comprehensive testing completed.

### 🔧 Technical Implementation

#### URL Pattern Recognition
```javascript
// Flexible regex patterns handle ALL content URLs:
/^\/content\/(?:.*-)?(content_[0-9a-zA-Z]+_[0-9a-zA-Z]+)$/  // With slugs
/^\/content\/(content_[0-9a-zA-Z]+_[0-9a-zA-Z]+)$/         // Direct IDs
```

#### Database Integration
- **Published Filter**: `is_published = 1` for security
- **MySQL2 Compatibility**: Handles both production `[rows, fields]` and development `rows` formats
- **Snake Case Columns**: Uses `poster_url`, `is_published` database naming

#### Meta Tag Generation
**Automatic generation of content-specific tags:**
- **Open Graph**: `og:title`, `og:description`, `og:image`, `og:url`, `og:type`
- **Twitter Cards**: `twitter:title`, `twitter:description`, `twitter:image`
- **Additional Properties**: Image dimensions, card type, content-specific metadata

### 🚀 Future Compatibility Guarantee

**The social sharing feature is FULLY AUTOMATED and works for:**
- ✅ All existing content in database (9 current records)
- ✅ All future content (movies, TV series, any type)
- ✅ Any URL format the system generates
- ✅ All major social platforms (Facebook, WhatsApp, Telegram, Twitter, LinkedIn)

### 📊 Validation & Testing

#### Production Validation Results
```bash
# Debug Endpoint Test
curl "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx"
# ✅ Returns: Content-specific JSON with meta tags

# Social Sharing Test  
curl -A "facebookexternalhit/1.1" "[content_url]"
# ✅ Returns: HTML with content-specific title, poster, description
```

#### Cache Management
- **Platform Caching**: Social media platforms cache for 24-48 hours
- **Cache Refresh**: Use Facebook Sharing Debugger for immediate refresh
- **Testing Strategy**: Use cache-busting parameters `?v=1` for fresh tests

### 🔒 Stability Features

#### Fallback Protection
- **Graceful Degradation**: Serves standard React app if meta injection fails
- **Core Functionality**: Website remains fully functional regardless of sharing status
- **Error Handling**: Database errors don't break application
- **Non-Breaking**: Implementation preserves all existing features

#### Requirements for Future Content
- Content marked as `is_published = 1` in database
- Basic metadata present: `title`, `description`, `poster_url`
- **No additional configuration needed** - completely automated

### 📋 Maintenance Status

**✅ ZERO ONGOING MAINTENANCE REQUIRED**
- **Content Detection**: Automatic via regex patterns
- **Database Queries**: Dynamic per request
- **Meta Generation**: Based on actual content data  
- **HTML Injection**: Server-side for all content pages
- **Platform Support**: Universal Open Graph standard

**The social sharing feature is production-ready and future-proof for any content added to StreamDB.**

---

## 🚀 Deployment & Configuration

### Production Environment
```bash
# Server Specs
OS: Ubuntu 24.04.2 LTS
Node: v20.19.3
PM2: v6.0.8
MySQL: 8.0.42

# Application Status
PM2 Process: streamdb-online (online, 6h uptime)
Memory Usage: ~400MB total (MySQL: 297MB, Node: 85MB)
```

### Environment Variables
```bash
NODE_ENV=production
PORT=3001
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=stream_db
JWT_SECRET=secure_jwt_secret
FRONTEND_URL=https://streamdb.online
TMDB_API_KEY=api_key
OMDB_API_KEY=api_key
```

### Build & Deploy Process
```bash
# Development
npm run dev              # Frontend + backend
cd server && npm run dev

# Production
npm run build           # Frontend build
npm run deploy          # Build + SEO + deploy prep
pm2 restart streamdb-online  # Zero-downtime restart
```

---

## 🤖 Automated Systems

### SEO Automation (`scripts/`)
```bash
auto-seo-update.js       # Comprehensive SEO automation
generate-sitemap.js      # XML sitemap generation
ping-search-engines.js   # Search engine notifications
seo-health-check.js      # SEO diagnostics
verify-seo.js           # Implementation verification
```

### Server Maintenance (`Servers Maintenance Automation Script/`)
```bash
backend-maintenance.sh          # Automated maintenance
reverse-proxy-maintenance.sh    # Proxy maintenance
cron-configuration.sh          # Scheduled tasks
validation-scripts.sh          # Health checks
```

---

## 📊 Content Management

### Content Types
- **Movies**: Single video with metadata
- **Web Series**: Multi-season hierarchical structure
- **Requested**: User-requested content tracking

### Content Structure
```
Web Series
├── Season 1
│   ├── Episode 1 (title + secure video link)
│   ├── Episode 2
│   └── ...
├── Season 2
└── ...
```

### Categories (18 Total)
Action, Adventure, Animation, Comedy, Crime, Documentary, Drama, Family, Fantasy, History, Horror, Music, Mystery, Romance, Science Fiction, Thriller, War, Western

### Sections (6 Active)
1. Movies - Latest and popular movies
2. Web Series - Trending series and TV shows  
3. Requested - User requested content
4. New Releases - Latest additions
5. Drama - Drama category content

---

## 🔐 Security & Protection

### Network Security
- **Reverse Proxy Shield**: Backend IP hidden from public
- **Cloudflare Protection**: DDoS protection, traffic filtering
- **Firewall Rules**: Restrictive UFW with specific allowances
- **Database Security**: Socket-only, localhost connections

### Application Security
- **Authentication**: JWT + bcrypt + session management
- **Input Validation**: Comprehensive server-side validation
- **SQL Injection Prevention**: Prepared statements only
- **XSS Protection**: Content sanitization + CSP headers

### Video Content Security
- **Encrypted Links**: Server-side video URL encryption
- **Access Control**: Time-limited streaming tokens
- **Embed Protection**: Secure iframe with domain restrictions
- **Download Prevention**: Stream-only delivery

### DMCA Protection Strategy
- **IP Obfuscation**: Backend not publicly associated with domain
- **DNS Protection**: Domain resolves only to Cloudflare
- **Proxy Routing**: All traffic through reverse proxy
- **Content Management**: Secure admin for compliance

---

## 📈 Monitoring & Analytics

### System Health
- **PM2 Monitoring**: Process health, resource usage
- **Database Metrics**: MySQL performance, query optimization
- **Server Monitoring**: System resources, service status
- **Security Logs**: 1158 active security records

### Performance Metrics
- **Response Times**: API endpoint performance
- **Database Performance**: Query execution analysis
- **User Analytics**: Content engagement tracking
- **Error Tracking**: Application error monitoring

---

## 🚨 Troubleshooting Guide

### Common Issues & Solutions

#### Database Problems
```bash
systemctl status mysql                    # Check service
node server/database-verification.js     # Test connection
ls -la /var/run/mysqld/mysqld.sock      # Check socket
```

#### PM2 Process Issues
```bash
pm2 status                    # Check status
pm2 logs streamdb-online     # View logs
pm2 restart streamdb-online  # Restart app
pm2 show streamdb-online     # Detailed info
```

#### Web Server Problems
```bash
nginx -t                      # Test config
systemctl restart nginx      # Restart service
tail -f /var/log/nginx/error.log  # Check logs
```

### Emergency Procedures
1. **Service Restart**: `pm2 restart streamdb-online`
2. **Web Server Restart**: `systemctl restart nginx apache2`
3. **Database Recovery**: Restore from backup
4. **Rollback**: Use previous version backup
5. **Traffic Rerouting**: Cloudflare maintenance mode

---

## 🔧 Development Workflow

### Local Setup
```bash
# Development Environment
git clone <repo>
cd Streaming_DB
npm install
cp server/.env.example server/.env
# Configure local database credentials

# Start Development
npm run dev          # Frontend (http://localhost:5173)
cd server && npm run dev  # Backend (http://localhost:3001)
```

### Database Setup
```sql
CREATE DATABASE streamdb_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
SOURCE database/complete_schema.sql;
SOURCE database/initial_data.sql;
node database/run_migration.js
```

### Testing Strategy
- **Backend**: Jest + Supertest for API testing
- **Frontend**: React Testing Library for components
- **Integration**: End-to-end workflow testing
- **Database**: Isolated test database

---

## 📋 Key Configuration Files

```bash
# Frontend
package.json           # Dependencies and scripts
vite.config.ts        # Build configuration
tailwind.config.ts    # Styling configuration
tsconfig.json         # TypeScript settings

# Backend  
server/package.json   # Backend dependencies
server/.env           # Environment variables
ecosystem.config.js   # PM2 configuration

# Infrastructure
nginx-reverse-proxy-config.conf  # Proxy configuration
deployment/deploy.sh             # Deployment automation
database/complete_schema.sql     # Database schema
```

---

## 🔮 Future Enhancements

### Planned Features
- User registration and profiles
- Content rating and review system
- AI-powered recommendation engine
- React Native mobile application
- CDN integration for global delivery

### Technical Improvements
- Microservices architecture
- Redis caching layer
- Elasticsearch integration
- WebSocket real-time features
- API versioning system

---

*This comprehensive memory document captures all aspects of the StreamDB Online project. It should be automatically updated as the project evolves to maintain accuracy and completeness.*