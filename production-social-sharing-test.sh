#!/bin/bash
# Safe Production Social Sharing Diagnostic Test
# This script ONLY reads data - makes NO changes to your system

echo "=============================================="
echo "🔍 STREAMDB SOCIAL SHARING DIAGNOSTIC TEST"
echo "=============================================="
echo "📅 Test Date: $(date)"
echo "🌐 Domain: streamdb.online"
echo ""

echo "1️⃣ TESTING SERVER STATUS..."
echo "----------------------------------------"
pm2 status | grep streamdb-online
echo ""

echo "2️⃣ TESTING DATABASE CONNECTION..."
echo "----------------------------------------"
curl -s "https://streamdb.online/api/health" | head -5
echo ""

echo "3️⃣ FINDING PUBLISHED CONTENT VIA API..."
echo "----------------------------------------"
echo "📊 Checking published content via content API..."
echo "🔍 Fetching recent published content:"
curl -s "https://streamdb.online/api/content?published=true&limit=5" | head -20
echo ""
echo "📋 Note: Look for 'id' fields in the JSON above (format: content_xxxxx_xxxxx)"
echo ""

echo "4️⃣ TESTING WITH KNOWN CONTENT URLs..."
echo "----------------------------------------"
echo "🧪 Testing the URL you mentioned: mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"
echo ""
echo "📋 Testing debug endpoint:"
curl -s "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx"
echo ""
echo ""

echo "5️⃣ TESTING SOCIAL SHARING DETECTION..."
echo "----------------------------------------"
echo "🔍 Making request with Facebook crawler user agent..."
echo "📊 Testing URL: https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"

# Make the request and capture response
echo "📋 Requesting page with Facebook user agent..."
curl -s -A "facebookexternalhit/1.1" "https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx" > /tmp/social_test.html

echo "📋 Checking for content-specific meta tags..."
if grep -q "og:title.*StreamDB.*Free Movies" /tmp/social_test.html; then
    echo "❌ PROBLEM: Found generic StreamDB title (not content-specific)"
else
    echo "✅ Good: No generic title found"
fi

echo "📋 Meta tag analysis:"
echo "🏷️ Page Title:"
grep "<title>" /tmp/social_test.html
echo "🏷️ OG Title:"
grep "og:title" /tmp/social_test.html
echo "🏷️ OG Image:"
grep "og:image" /tmp/social_test.html
echo "🏷️ OG Description:"
grep "og:description" /tmp/social_test.html

echo ""
echo "6️⃣ CHECKING SERVER LOGS..."
echo "----------------------------------------"
echo "📋 Checking recent logs for social sharing detection:"
pm2 logs streamdb-online --lines 10 | grep -E "Social sharing|Content found|Generated meta|Debug endpoint" || echo "❌ No social sharing detection found in logs"

echo ""
echo "7️⃣ TESTING CONTENT ID EXTRACTION..."
echo "----------------------------------------"
echo "🧪 Testing if server recognizes the content URL pattern..."
echo "📋 Making request and checking logs immediately after..."
curl -s -A "facebookexternalhit/1.1" "https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx" > /dev/null
echo "📊 Fresh logs (should show social sharing detection):"
pm2 logs streamdb-online --lines 5 | tail -5

echo ""
echo "=============================================="
echo "✅ DIAGNOSTIC TEST COMPLETED"
echo "=============================================="
echo ""
echo "📋 ANALYSIS:"
echo "1. Check if Step 3 shows any published content IDs"
echo "2. Check if Step 4 debug endpoint finds the content"
echo "3. Check if Step 5 shows content-specific meta tags"
echo "4. Check if Step 6 shows social sharing detection in logs"
echo "5. Check if Step 7 shows fresh social sharing detection"
echo ""
echo "🚨 IMPORTANT: This script made NO changes to your system"
echo "   It only READ data for diagnostic purposes"
echo ""
echo "🎯 NEXT ACTIONS:"
echo "   If content is found but meta tags are generic, the issue is in meta tag injection"
echo "   If no social sharing detection in logs, the issue is in URL pattern matching"
echo "   If debug endpoint shows 'Content not found', the issue is in database query"

# Cleanup
rm -f /tmp/social_test.html