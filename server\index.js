const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const { exec } = require('child_process');
require('dotenv').config();

const db = require('./config/database');
const authRoutes = require('./routes/auth');
const contentRoutes = require('./routes/content');
const adminRoutes = require('./routes/admin');
// const uploadRoutes = require('./routes/upload'); // Temporarily disabled due to sharp dependency
const categoryRoutes = require('./routes/categories');
// trackingRoutes removed

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy headers from Nginx
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable helmet CSP to avoid conflicts
  crossOriginEmbedderPolicy: false
}));

// Custom CSP middleware with Monetag support - applied early to ensure it's not overridden
app.use((req, res, next) => {
  // Set comprehensive CSP with Google Fonts support, Kaspersky compatibility, and Monetag ads
  res.setHeader('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://ff.kis.v2.scr.kaspersky-labs.com https://gc.kis.v2.scr.kaspersky-labs.com; " +
    "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://ff.kis.v2.scr.kaspersky-labs.com https://gc.kis.v2.scr.kaspersky-labs.com; " +
    "font-src 'self' data: https://fonts.gstatic.com https://fonts.googleapis.com; " +
    "img-src 'self' data: https: blob:; " +
    "connect-src 'self' https: wss: wss://ff.kis.v2.scr.kaspersky-labs.com wss://gc.kis.v2.scr.kaspersky-labs.com https://pertawee.net https://al5sm.com; " +
    "media-src 'self' https: blob:; " +
    "object-src 'none'; " +
    "frame-src 'self' https:; " +
    "base-uri 'self'; " +
    "form-action 'self'; " +
    "frame-ancestors 'self'"
  );
  next();
});



// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs (increased for development)
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // Increased limit for admin panel usage (was causing 429 errors)
  message: 'Too many authentication attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/auth', authLimiter);
app.use('/api', limiter);

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'https://streamdb.online', // Your actual domain
      'https://www.streamdb.online', // With www
      'http://streamdb.online', // HTTP version for development
      'http://www.streamdb.online', // HTTP www version
      process.env.FRONTEND_URL
    ].filter(Boolean);

    // Log CORS requests for debugging
    console.log(`CORS request from origin: ${origin}`);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging
app.use(morgan('combined'));

// Session configuration - Using memory store permanently
// Memory store is sufficient for this application as sessions are primarily used for
// tracking and security logging, not critical user state persistence
console.log('✓ Using memory session store (sessions reset on server restart)');
const sessionStore = undefined; // Use default memory store

app.use(session({
  key: 'streamdb_session',
  secret: process.env.SESSION_SECRET || 'your-secret-key-change-this',
  store: sessionStore,
  resave: false,
  saveUninitialized: false,
  cookie: {
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: 'lax'
  }
}));

// Static file serving for React build
const distPath = path.join(__dirname, '..', 'dist');
console.log('Serving static files from:', distPath);

// Configure static file serving with proper MIME types and security headers
app.use(express.static(distPath, {
  setHeaders: (res, filePath) => {
    // Set proper MIME types for different file extensions
    if (filePath.endsWith('.js') || filePath.endsWith('.mjs')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
      res.setHeader('X-Content-Type-Options', 'nosniff');
    } else if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
    } else if (filePath.endsWith('.json')) {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
    } else if (filePath.endsWith('.xml')) {
      res.setHeader('Content-Type', 'application/xml; charset=utf-8');
    } else if (filePath.endsWith('.txt')) {
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    } else if (filePath.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (filePath.endsWith('.svg')) {
      res.setHeader('Content-Type', 'image/svg+xml');
    } else if (filePath.endsWith('.ico')) {
      res.setHeader('Content-Type', 'image/x-icon');
    } else if (filePath.endsWith('.webmanifest')) {
      res.setHeader('Content-Type', 'application/manifest+json');
    } else if (filePath.endsWith('.tsx') || filePath.endsWith('.ts')) {
      // Prevent serving TypeScript files directly
      res.status(404).end();
      return;
    }

    // Add cache headers for static assets
    if (filePath.includes('/assets/')) {
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    } else {
      res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour
    }
  }
}));

// Favicon and icon routes with proper MIME types
app.get('/favicon.ico', (req, res) => {
  res.setHeader('Content-Type', 'image/x-icon');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // 24 hours
  res.sendFile(path.join(distPath, 'favicon.ico'));
});

// SEO-critical files with proper MIME types
app.get('/sitemap.xml', (req, res) => {
  res.setHeader('Content-Type', 'application/xml; charset=utf-8');
  res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour
  res.sendFile(path.join(distPath, 'sitemap.xml'), (err) => {
    if (err) {
      console.error('Error serving sitemap.xml:', err);
      res.status(404).send('Sitemap not found');
    }
  });
});

app.get('/robots.txt', (req, res) => {
  res.setHeader('Content-Type', 'text/plain; charset=utf-8');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // 24 hours
  res.sendFile(path.join(distPath, 'robots.txt'), (err) => {
    if (err) {
      console.error('Error serving robots.txt:', err);
      res.status(404).send('Robots.txt not found');
    }
  });
});

app.get('/favicon-16x16.png', (req, res) => {
  res.setHeader('Content-Type', 'image/png');
  res.setHeader('Cache-Control', 'public, max-age=86400');
  res.sendFile(path.join(distPath, 'favicon-16x16.png'));
});

app.get('/favicon-32x32.png', (req, res) => {
  res.setHeader('Content-Type', 'image/png');
  res.setHeader('Cache-Control', 'public, max-age=86400');
  res.sendFile(path.join(distPath, 'favicon-32x32.png'));
});

app.get('/apple-touch-icon.png', (req, res) => {
  res.setHeader('Content-Type', 'image/png');
  res.setHeader('Cache-Control', 'public, max-age=86400');
  res.sendFile(path.join(distPath, 'apple-touch-icon.png'));
});

// Debug endpoint to test meta tag injection
app.get('/api/debug/meta/:contentId', async (req, res) => {
  try {
    const { contentId } = req.params;
    
    console.log(`🔍 Debug endpoint: Looking for content ID: ${contentId}`);
    
    // Fetch content data with additional debugging
    const result = await db.execute(
      'SELECT id, title, description, year, type, poster_url, image, is_published FROM content WHERE id = ? AND is_published = 1 LIMIT 1',
      [contentId]
    );
    
    // Handle both mysql2 result formats: [rows, fields] or rows directly
    let contentRows;
    if (Array.isArray(result) && Array.isArray(result[0])) {
      contentRows = result[0]; // [rows, fields] format
    } else {
      contentRows = result; // rows only format
    }
    
    console.log(`📊 Debug endpoint: Query returned ${contentRows ? contentRows.length : 0} rows`);
    if (contentRows && contentRows.length > 0) {
      console.log(`📋 Debug endpoint: Found content - Title: ${contentRows[0].title}, Published: ${contentRows[0].is_published}`);
    }
    
    if (contentRows && contentRows.length > 0) {
      const content = contentRows[0];
      const metaTags = generateContentMetaTags(content);
      
      res.json({
        success: true,
        contentId,
        content: {
          title: content.title,
          type: content.type,
          year: content.year,
          is_published: content.is_published
        },
        metaTags,
        testUrl: `https://streamdb.online/content/${contentId}`
      });
    } else {
      console.log(`❌ Debug endpoint: Content not found for ID: ${contentId}`);
      
      res.status(404).json({
        success: false,
        error: 'Content not found',
        contentId,
        debug: {
          searchedId: contentId,
          message: 'No content found with this exact ID'
        }
      });
    }
  } catch (error) {
    console.error('Debug meta endpoint error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    distPath: distPath,
    distExists: fs.existsSync(distPath)
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/content', contentRoutes);
app.use('/api/admin', adminRoutes);
// app.use('/api/upload', uploadRoutes); // Temporarily disabled due to sharp dependency
app.use('/api/categories', categoryRoutes);
app.use('/api/sections', require('./routes/sections'));
app.use('/api/episodes', require('./routes/episodes'));
app.use('/api/tmdb', require('./routes/tmdb'));
app.use('/api/omdb', require('./routes/omdb'));
// app.use('/api/database', require('./routes/database-diagnostic'));

// Minimal public content canonical redirect: id-only -> slug+id
// Scoped only to public content paths; does not affect /api or admin endpoints
app.get('/content/:identifier', async (req, res, next) => {
  try {
    const { identifier } = req.params;

    // Only handle pure id form like "content_..."; skip if already slug+id
    if (!identifier || !identifier.startsWith('content_')) {
      return next();
    }

    // Lookup title by id (published content preferred)
    let rows = await db.execute('SELECT title FROM content WHERE id = ? LIMIT 1', [identifier]);
    let record = null;
    if (Array.isArray(rows)) {
      // Support both mysql2 shapes: rows or [rows, fields]
      if (Array.isArray(rows[0])) {
        record = rows[0][0] || null;
      } else {
        record = rows[0] || null;
      }
    }

    if (!record || !record.title) {
      // If we can't resolve, fall back to SPA to handle gracefully
      return next();
    }

    // Local slugify (keep logic aligned with client)
    const slugifyTitle = (title) => {
      if (!title) return '';
      return title
        .toLowerCase()
        .normalize('NFKD')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '');
    };

    const slug = slugifyTitle(record.title);
    const targetPath = slug ? `/content/${slug}-${identifier}` : `/content/${identifier}`;

    // Preserve query string if present
    const qsIndex = req.originalUrl.indexOf('?');
    const qs = qsIndex !== -1 ? req.originalUrl.substring(qsIndex) : '';

    // Issue permanent redirect
    return res.redirect(301, `${targetPath}${qs}`);
  } catch (err) {
    console.error('Canonical redirect error:', err.message);
    return next();
  }
});

// tracking routes removed

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);

  // Handle specific error types
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation Error',
      message: err.message,
      details: err.details || []
    });
  }

  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    });
  }

  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      error: 'File Too Large',
      message: 'File size exceeds the maximum allowed limit'
    });
  }

  // Default error response
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production'
      ? 'Something went wrong'
      : err.message
  });
});

// Helper function to extract content ID from URL path
function extractContentIdFromPath(pathname) {
  console.log(`🔍 [DEBUG] Extracting content ID from path: ${pathname}`);
  
  // Updated regex to match actual content ID format: content_1755547321661_rgeqe1diz
  // More flexible pattern to match: content_[numbers/letters]_[letters/numbers]
  const contentMatch = pathname.match(/^\/content\/(?:.*-)?(content_[0-9a-zA-Z]+_[0-9a-zA-Z]+)$/);
  if (contentMatch) {
    console.log(`🎯 [DEBUG] First pattern matched: ${contentMatch[1]}`);
    return contentMatch[1];
  }
  
  // Also handle direct ID paths like /content/content_12345...
  const directIdMatch = pathname.match(/^\/content\/(content_[0-9a-zA-Z]+_[0-9a-zA-Z]+)$/);
  if (directIdMatch) {
    console.log(`🎯 [DEBUG] Second pattern matched: ${directIdMatch[1]}`);
    return directIdMatch[1];
  }
  
  console.log(`❌ [DEBUG] No pattern matched for path: ${pathname}`);
  return null;
}

// Helper function to generate content meta tags
function generateContentMetaTags(content) {
  const title = content.title || 'StreamDB Content';
  const description = content.description ? 
    content.description.substring(0, 160) + (content.description.length > 160 ? '...' : '') :
    `Watch ${title} on StreamDB - Free streaming in HD quality.`;
  
  // Generate absolute image URL
  let imageUrl = 'https://streamdb.online/android-chrome-512x512.png'; // Default fallback
  if (content.poster_url || content.image) {
    const poster = content.poster_url || content.image;
    if (poster.startsWith('http')) {
      imageUrl = poster;
    } else if (poster.startsWith('/')) {
      imageUrl = `https://streamdb.online${poster}`;
    } else {
      imageUrl = `https://streamdb.online/${poster}`;
    }
  }
  
  const currentUrl = `https://streamdb.online/content/${content.id}`;
  const contentType = content.type === 'series' ? 'TV Series' : 'Movie';
  const year = content.year ? ` (${content.year})` : '';
  const fullTitle = `${title}${year} - ${contentType} | StreamDB`;
  
  return {
    title: fullTitle,
    description: description,
    ogTitle: title,
    ogDescription: description,
    ogImage: imageUrl,
    ogUrl: currentUrl,
    ogType: content.type === 'series' ? 'video.tv_show' : 'video.movie'
  };
}

// Helper function to inject meta tags into HTML
function injectMetaTags(html, metaTags) {
  // Replace the existing Open Graph tags with content-specific ones
  let updatedHtml = html
    // Update page title
    .replace(
      /<title>[^<]*<\/title>/,
      `<title>${metaTags.title}</title>`
    )
    // Update meta description
    .replace(
      /<meta name="description" content="[^"]*"\s*\/?>/,
      `<meta name="description" content="${metaTags.description}" />`
    )
    // Update Open Graph tags
    .replace(
      /<meta property="og:title" content="[^"]*"\s*\/?>/,
      `<meta property="og:title" content="${metaTags.ogTitle}" />`
    )
    .replace(
      /<meta property="og:description" content="[^"]*"\s*\/?>/,
      `<meta property="og:description" content="${metaTags.ogDescription}" />`
    )
    .replace(
      /<meta property="og:image" content="[^"]*"\s*\/?>/,
      `<meta property="og:image" content="${metaTags.ogImage}" />`
    )
    .replace(
      /<meta property="og:url" content="[^"]*"\s*\/?>/,
      `<meta property="og:url" content="${metaTags.ogUrl}" />`
    )
    .replace(
      /<meta property="og:type" content="[^"]*"\s*\/?>/,
      `<meta property="og:type" content="${metaTags.ogType}" />`
    )
    // Update Twitter Card tags
    .replace(
      /<meta property="twitter:title" content="[^"]*"\s*\/?>/,
      `<meta property="twitter:title" content="${metaTags.ogTitle}" />`
    )
    .replace(
      /<meta property="twitter:description" content="[^"]*"\s*\/?>/,
      `<meta property="twitter:description" content="${metaTags.ogDescription}" />`
    )
    .replace(
      /<meta property="twitter:image" content="[^"]*"\s*\/?>/,
      `<meta property="twitter:image" content="${metaTags.ogImage}" />`
    )
    .replace(
      /<meta property="twitter:url" content="[^"]*"\s*\/?>/,
      `<meta property="twitter:url" content="${metaTags.ogUrl}" />`
    );
  
  // Add additional Open Graph properties if not present
  const additionalMetaTags = `
    <!-- Content-Specific Meta Tags -->
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:type" content="image/jpeg" />
    <meta name="twitter:card" content="summary_large_image" />
    <!-- End Content-Specific Meta Tags -->`;
  
  // Insert additional tags before the closing </head> tag
  updatedHtml = updatedHtml.replace('</head>', `${additionalMetaTags}\n  </head>`);
  
  return updatedHtml;
}

// Catch-all handler: send back React's index.html file for client-side routing
app.get('*', async (req, res) => {
  // Only serve index.html for non-API routes and non-static assets
  const isApiRoute = req.path.startsWith('/api/');
  const isStaticAsset = req.path.startsWith('/assets/') ||
                       req.path.includes('.js') ||
                       req.path.includes('.css') ||
                       req.path.includes('.png') ||
                       req.path.includes('.jpg') ||
                       req.path.includes('.jpeg') ||
                       req.path.includes('.svg') ||
                       req.path.includes('.ico') ||
                       req.path.includes('.webmanifest') ||
                       req.path.includes('.txt') ||
                       req.path.includes('.xml') ||
                       req.path.includes('.json') ||
                       req.path.includes('.woff') ||
                       req.path.includes('.woff2') ||
                       req.path.includes('.ttf') ||
                       req.path.includes('.eot');

  if (isApiRoute) {
    // API routes that don't exist should return 404 JSON
    res.status(404).json({
      error: 'Not Found',
      message: 'The requested API resource was not found'
    });
  } else if (isStaticAsset) {
    // Static assets that don't exist should return 404
    res.status(404).send('File not found');
  } else {
    // Check if this is a content page request
    const contentId = extractContentIdFromPath(req.path);
    console.log(`🔍 [DEBUG] Request path: ${req.path}`);
    console.log(`🔍 [DEBUG] User agent: ${req.get('User-Agent')}`);
    console.log(`🔍 [DEBUG] Extracted content ID: ${contentId}`);
    
    if (contentId) {
      // This is a content page - inject dynamic meta tags
      console.log(`🔍 Social sharing request detected for content: ${contentId}`);
      console.log(`🔍 [DEBUG] Starting meta tag injection process...`);
      
      try {
        // Fetch content data from database
        const result = await db.execute(
          'SELECT id, title, description, year, type, poster_url, image FROM content WHERE id = ? AND is_published = 1 LIMIT 1',
          [contentId]
        );
        
        // Handle both mysql2 result formats: [rows, fields] or rows directly
        let contentRows;
        if (Array.isArray(result) && Array.isArray(result[0])) {
          contentRows = result[0]; // [rows, fields] format
        } else {
          contentRows = result; // rows only format
        }
        
        if (contentRows && contentRows.length > 0) {
          const content = contentRows[0];
          console.log(`✅ Content found: ${content.title}`);
          
          // Read the index.html file
          const indexPath = path.join(distPath, 'index.html');
          
          try {
            const htmlContent = fs.readFileSync(indexPath, 'utf8');
            
            // Generate meta tags for this content
            const metaTags = generateContentMetaTags(content);
            console.log(`🏷️ Generated meta tags for: ${metaTags.ogTitle}`);
            
            // Inject the meta tags
            const updatedHtml = injectMetaTags(htmlContent, metaTags);
            
            // Send the modified HTML
            res.setHeader('Content-Type', 'text/html; charset=utf-8');
            res.send(updatedHtml);
            return;
            
          } catch (fileError) {
            console.error('Error reading index.html for meta injection:', fileError);
            // Fall through to default behavior
          }
        } else {
          console.log(`❌ Content not found for ID: ${contentId}`);
          // Fall through to default behavior
        }
      } catch (dbError) {
        console.error('Database error during meta tag injection:', dbError);
        // Fall through to default behavior
      }
    } else {
      console.log(`📝 [DEBUG] No content ID found - serving default React app`);
    }
    
    // Default behavior: serve standard index.html for client-side routing
    const indexPath = path.join(distPath, 'index.html');
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.sendFile(indexPath, (err) => {
      if (err) {
        console.error('Error serving index.html:', err);
        res.status(500).send('Internal Server Error');
      }
    });
  }
});

// Database connection test
async function testDatabaseConnection() {
  try {
    await db.execute('SELECT 1');
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.warn('⚠️ Continuing without database for static file testing...');
    return false;
  }
}

// Start server
async function startServer() {
  try {
    const dbConnected = await testDatabaseConnection();

    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
      console.log(`🌐 Static files: http://localhost:${PORT}`);
      console.log(`🎯 Monetag ads: CSP configured for pertawee.net and al5sm.com`);
      if (dbConnected) {
        console.log(`💾 Database: ${process.env.DB_NAME}@${process.env.DB_HOST}`);
      } else {
        console.log(`⚠️ Database: Not connected (static file mode)`);
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();