#!/bin/bash

# ============================================================================
# StreamDB Production Server - Disk Space Analysis & Report Script
# Server: backend1maindb (***********) - FastPanel CP with MySQL
# Purpose: Comprehensive disk usage analysis and cleanup recommendations
# Author: Generated for streamdb.online
# Version: 1.1
# ============================================================================

set -euo pipefail

# Configuration
SCRIPT_NAME="StreamDB Disk Space Analyzer"
SCRIPT_VERSION="1.1"
REPORT_DIR="/tmp/streamdb-disk-analysis"
REPORT_FILE="$REPORT_DIR/disk-analysis-$(date +%Y%m%d-%H%M%S).txt"
HTML_REPORT="$REPORT_DIR/disk-analysis-$(date +%Y%m%d-%H%M%S).html"

# Server-specific paths (FastPanel + StreamDB)
STREAMDB_PATH="/var/www/streamdb_root/data/www/streamdb.online"
FASTPANEL_PATH="/usr/local/mgr5"
MYSQL_DATA_DIR="/var/lib/mysql"
NGINX_LOG_DIR="/var/log/nginx"
MYSQL_LOG_DIR="/var/log/mysql"
PM2_LOG_DIR="$STREAMDB_PATH/logs"
MAINTENANCE_LOG_DIR="/var/log/streamdb-maintenance"
BACKUP_DIR="/var/backups/streamdb-maintenance"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Size thresholds for analysis (in MB)
LARGE_FILE_THRESHOLD=100
CRITICAL_SIZE_THRESHOLD=1000
WARNING_SIZE_THRESHOLD=500

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$REPORT_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }

print_header() {
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
    log_info "$1"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
    log_info "Analyzing: $1"
}

format_size() {
    local size_kb="$1"
    if (( size_kb >= 1048576 )); then
        printf "%.2f GB" "$(echo "scale=2; $size_kb / 1048576" | bc -l)"
    elif (( size_kb >= 1024 )); then
        printf "%.2f MB" "$(echo "scale=2; $size_kb / 1024" | bc -l)"
    else
        printf "%d KB" "$size_kb"
    fi
}

get_size_mb() {
    local path="$1"
    if [[ -e "$path" ]]; then
        du -sm "$path" 2>/dev/null | cut -f1 || echo "0"
    else
        echo "0"
    fi
}

get_file_count() {
    local path="$1"
    if [[ -d "$path" ]]; then
        find "$path" -type f 2>/dev/null | wc -l
    else
        echo "0"
    fi
}

get_safety_rating() {
    local path="$1"
    local file_type="$2"
    
    case "$file_type" in
        "log") echo "SAFE" ;;
        "cache") echo "SAFE" ;;
        "temp") echo "SAFE" ;;
        "backup") echo "MEDIUM" ;;
        "config") echo "DANGER" ;;
        "database") echo "DANGER" ;;
        "application") echo "DANGER" ;;
        *) echo "REVIEW" ;;
    esac
}

setup_report_directory() {
    mkdir -p "$REPORT_DIR"
    chmod 755 "$REPORT_DIR"
    
    # Initialize reports
    echo "StreamDB Disk Space Analysis Report" > "$REPORT_FILE"
    echo "Generated: $(date)" >> "$REPORT_FILE"
    echo "Server: backend1maindb (***********)" >> "$REPORT_FILE"
    echo "========================================" >> "$REPORT_FILE"
}

check_prerequisites() {
    local missing_tools=()
    
    # Check for required tools
    command -v du >/dev/null || missing_tools+=("du")
    command -v find >/dev/null || missing_tools+=("find")
    command -v df >/dev/null || missing_tools+=("df")
    command -v bc >/dev/null || missing_tools+=("bc")
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Installing missing tools..."
        apt-get update && apt-get install -y "${missing_tools[@]}"
    fi
}

# ============================================================================
# DISK ANALYSIS FUNCTIONS
# ============================================================================

analyze_overall_disk_usage() {
    print_section "Overall Disk Usage Analysis"
    
    echo -e "${CYAN}FILESYSTEM OVERVIEW:${NC}"
    df -h | tee -a "$REPORT_FILE"
    
    echo -e "\n${CYAN}DETAILED DISK USAGE BY MOUNT POINT:${NC}"
    df -h --output=source,fstype,size,used,avail,pcent,target | tee -a "$REPORT_FILE"
    
    # Get root filesystem usage percentage
    local root_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if (( root_usage >= 90 )); then
        echo -e "${RED}⚠️  CRITICAL: Root filesystem is ${root_usage}% full!${NC}"
        log_error "CRITICAL: Root filesystem usage at ${root_usage}%"
    elif (( root_usage >= 80 )); then
        echo -e "${YELLOW}⚠️  WARNING: Root filesystem is ${root_usage}% full${NC}"
        log_warn "WARNING: Root filesystem usage at ${root_usage}%"
    else
        echo -e "${GREEN}✅ Root filesystem usage is acceptable (${root_usage}%)${NC}"
        log_success "Root filesystem usage is acceptable (${root_usage}%)"
    fi
}

analyze_directory_sizes() {
    print_section "Directory Size Analysis"
    
    echo -e "${CYAN}TOP LARGEST DIRECTORIES:${NC}"
    
    # Analyze major directories
    local dirs_to_analyze=(
        "/var/log:log:System logs"
        "/var/cache:cache:System cache"
        "/tmp:temp:Temporary files"
        "/var/tmp:temp:Variable temporary files"
        "$MYSQL_DATA_DIR:database:MySQL database files"
        "$NGINX_LOG_DIR:log:Nginx web server logs"
        "$MYSQL_LOG_DIR:log:MySQL server logs"
        "$PM2_LOG_DIR:log:PM2 application logs"
        "$MAINTENANCE_LOG_DIR:log:Maintenance script logs"
        "$BACKUP_DIR:backup:StreamDB maintenance backups"
        "$STREAMDB_PATH:application:StreamDB application files"
        "$FASTPANEL_PATH:application:FastPanel control panel"
        "/var/www:application:Web server document roots"
        "/home:application:User home directories"
        "/opt:application:Optional software packages"
        "/usr/local:application:Local software installations"
    )
    
    printf "%-50s %10s %15s %10s %s\n" "Directory Path" "Size" "Files" "Safety" "Recommendation"
    printf "%-50s %10s %15s %10s %s\n" "$(printf '%*s' 50 '' | tr ' ' '-')" "$(printf '%*s' 10 '' | tr ' ' '-')" "$(printf '%*s' 15 '' | tr ' ' '-')" "$(printf '%*s' 10 '' | tr ' ' '-')" "$(printf '%*s' 30 '' | tr ' ' '-')"
    
    for dir_info in "${dirs_to_analyze[@]}"; do
        IFS=':' read -r dir_path dir_type description <<< "$dir_info"
        
        if [[ -d "$dir_path" ]]; then
            local size_mb=$(get_size_mb "$dir_path")
            local file_count=$(get_file_count "$dir_path")
            local safety=$(get_safety_rating "$dir_path" "$dir_type")
            local recommendation=""
            
            # Determine recommendation based on size and type
            if (( size_mb >= CRITICAL_SIZE_THRESHOLD )); then
                case "$dir_type" in
                    "log") recommendation="Review and clean old logs (>30 days)" ;;
                    "cache") recommendation="Safe to clear cache files" ;;
                    "temp") recommendation="Safe to clean temporary files (>7 days)" ;;
                    "backup") recommendation="Review backup retention policy" ;;
                    *) recommendation="Manual review required" ;;
                esac
            elif (( size_mb >= WARNING_SIZE_THRESHOLD )); then
                case "$dir_type" in
                    "log") recommendation="Monitor log rotation settings" ;;
                    "cache") recommendation="Consider cache cleanup" ;;
                    "temp") recommendation="Regular cleanup recommended" ;;
                    *) recommendation="Monitor growth" ;;
                esac
            else
                recommendation="No action needed"
            fi
            
            printf "%-50s %10s %10s files [%8s] %s\n" \
                "$dir_path" \
                "$(format_size $((size_mb * 1024)))" \
                "$file_count" \
                "$safety" \
                "$recommendation" | tee -a "$REPORT_FILE"
        fi
    done
}

analyze_large_files() {
    print_section "Large Files Analysis (>${LARGE_FILE_THRESHOLD}MB)"
    
    echo -e "${CYAN}TOP 20 LARGEST FILES:${NC}"
    
    printf "%-60s %10s %12s %10s %s\n" "File Path" "Size" "Modified" "Safety" "Recommendation"
    printf "%-60s %10s %12s %10s %s\n" "$(printf '%*s' 60 '' | tr ' ' '-')" "$(printf '%*s' 10 '' | tr ' ' '-')" "$(printf '%*s' 12 '' | tr ' ' '-')" "$(printf '%*s' 10 '' | tr ' ' '-')" "$(printf '%*s' 30 '' | tr ' ' '-')"
    
    # Find large files
    find / -type f -size +${LARGE_FILE_THRESHOLD}M 2>/dev/null | while read -r file; do
        if [[ -f "$file" ]]; then
            local size_mb=$(du -sm "$file" 2>/dev/null | cut -f1)
            local modified=$(stat -c %y "$file" 2>/dev/null | cut -d' ' -f1)
            local file_type="unknown"
            local safety="REVIEW"
            local recommendation="Manual review required"
            
            # Determine file type and safety
            case "$file" in
                *.log|*.log.*) 
                    file_type="log"
                    safety="SAFE"
                    recommendation="Can be rotated/compressed"
                    ;;
                *.gz|*.tar|*.zip|*.bz2)
                    file_type="archive"
                    safety="MEDIUM"
                    recommendation="Review if backup is needed"
                    ;;
                *.sql|*.sql.gz)
                    file_type="database"
                    safety="DANGER"
                    recommendation="Database backup - DO NOT DELETE"
                    ;;
                *.mp4|*.mkv|*.avi|*.mov)
                    file_type="media"
                    safety="MEDIUM"
                    recommendation="Review if content file is needed"
                    ;;
                */mysql/*|*/mysql-bin.*)
                    file_type="database"
                    safety="DANGER"
                    recommendation="MySQL data file - DO NOT DELETE"
                    ;;
                */cache/*|*/tmp/*)
                    file_type="cache"
                    safety="SAFE"
                    recommendation="Safe to delete"
                    ;;
                *)
                    file_type="unknown"
                    safety="REVIEW"
                    recommendation="Manual inspection required"
                    ;;
            esac
            
            printf "%-60s %10s %12s [%8s] %s\n" \
                "$file" \
                "$(format_size $((size_mb * 1024)))" \
                "$modified" \
                "$safety" \
                "$recommendation"
        fi
    done | head -20 | tee -a "$REPORT_FILE"
}

analyze_log_files() {
    print_section "Log Files Analysis"
    
    echo -e "${CYAN}LOG FILE ANALYSIS:${NC}"
    
    printf "%-40s %10s %12s %12s %s\n" "Log Directory" "Size" "Files" "Oldest" "Recommendation"
    printf "%-40s %10s %12s %12s %s\n" "$(printf '%*s' 40 '' | tr ' ' '-')" "$(printf '%*s' 10 '' | tr ' ' '-')" "$(printf '%*s' 12 '' | tr ' ' '-')" "$(printf '%*s' 12 '' | tr ' ' '-')" "$(printf '%*s' 30 '' | tr ' ' '-')"
    
    local log_dirs=(
        "$NGINX_LOG_DIR:Nginx web server logs"
        "$MYSQL_LOG_DIR:MySQL database logs"
        "$PM2_LOG_DIR:PM2 application logs"
        "$MAINTENANCE_LOG_DIR:Maintenance script logs"
        "/var/log:System logs"
        "/var/log/apache2:Apache logs (if present)"
        "/var/log/fastpanel:FastPanel logs"
    )
    
    for log_info in "${log_dirs[@]}"; do
        IFS=':' read -r log_dir description <<< "$log_info"
        
        if [[ -d "$log_dir" ]]; then
            local total_size=$(get_size_mb "$log_dir")
            local file_count=$(find "$log_dir" -type f -name "*.log*" 2>/dev/null | wc -l)
            local oldest_file=""
            local cleanup_recommendation=""
            
            if [[ $file_count -gt 0 ]]; then
                oldest_file=$(find "$log_dir" -type f -name "*.log*" -printf '%T@ %p\n' 2>/dev/null | sort -n | head -1 | cut -d' ' -f2- | xargs stat -c %y 2>/dev/null | cut -d' ' -f1)
                
                # Determine cleanup recommendation
                if (( total_size >= 1000 )); then
                    cleanup_recommendation="URGENT: Clean logs older than 30 days"
                elif (( total_size >= 500 )); then
                    cleanup_recommendation="Clean logs older than 60 days"
                elif (( file_count >= 100 )); then
                    cleanup_recommendation="Clean logs older than 90 days"
                else
                    cleanup_recommendation="Monitor log rotation"
                fi
            else
                oldest_file="No log files"
                cleanup_recommendation="No action needed"
            fi
            
            printf "%-40s %10s %8s files %12s %s\n" \
                "$log_dir" \
                "$(format_size $((total_size * 1024)))" \
                "$file_count" \
                "$oldest_file" \
                "$cleanup_recommendation" | tee -a "$REPORT_FILE"
        fi
    done
}

analyze_temporary_files() {
    print_section "Temporary Files Analysis"
    
    echo -e "${CYAN}TEMPORARY FILE LOCATIONS:${NC}"
    
    printf "%-30s %10s %12s %12s %10s %s\n" "Location" "Size" "Files" "Oldest" "Safety" "Recommendation"
    printf "%-30s %10s %12s %12s %10s %s\n" "$(printf '%*s' 30 '' | tr ' ' '-')" "$(printf '%*s' 10 '' | tr ' ' '-')" "$(printf '%*s' 12 '' | tr ' ' '-')" "$(printf '%*s' 12 '' | tr ' ' '-')" "$(printf '%*s' 10 '' | tr ' ' '-')" "$(printf '%*s' 20 '' | tr ' ' '-')"
    
    local temp_locations=(
        "/tmp:System temporary files:SAFE"
        "/var/tmp:Variable temporary files:SAFE"
        "/var/cache:System cache files:SAFE"
        "/var/cache/apt:Package cache:SAFE"
        "/var/lib/php/sessions:PHP sessions:SAFE"
        "$STREAMDB_PATH/tmp:Application temp:SAFE"
        "$STREAMDB_PATH/cache:Application cache:SAFE"
    )
    
    for temp_info in "${temp_locations[@]}"; do
        IFS=':' read -r temp_dir description safety <<< "$temp_info"
        
        if [[ -d "$temp_dir" ]]; then
            local total_size=$(get_size_mb "$temp_dir")
            local file_count=$(get_file_count "$temp_dir")
            local oldest_file=""
            local cleanup_recommendation=""
            
            if [[ $file_count -gt 0 ]]; then
                oldest_file=$(find "$temp_dir" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | head -1 | cut -d' ' -f2- | xargs stat -c %y 2>/dev/null | cut -d' ' -f1)
                
                # Calculate age in days
                local oldest_timestamp=$(find "$temp_dir" -type f -printf '%T@\n' 2>/dev/null | sort -n | head -1)
                local current_timestamp=$(date +%s)
                local age_days=$(( (current_timestamp - ${oldest_timestamp%.*}) / 86400 ))
                
                if (( age_days >= 30 )); then
                    cleanup_recommendation="Clean files older than 7 days"
                elif (( total_size >= 500 )); then
                    cleanup_recommendation="Clean files older than 3 days"
                else
                    cleanup_recommendation="Monitor and clean as needed"
                fi
            else
                oldest_file="No files"
                cleanup_recommendation="No action needed"
            fi
            
            printf "%-30s %10s %8s files %12s [%8s] %s\n" \
                "$temp_dir" \
                "$(format_size $((total_size * 1024)))" \
                "$file_count" \
                "$oldest_file" \
                "$safety" \
                "$cleanup_recommendation" | tee -a "$REPORT_FILE"
        fi
    done
}

generate_cleanup_recommendations() {
    print_section "CLEANUP RECOMMENDATIONS SUMMARY"
    
    echo -e "${PURPLE}===== CLEANUP RECOMMENDATIONS =====${NC}"
    echo "" | tee -a "$REPORT_FILE"
    
    echo -e "${GREEN}SAFE TO CLEAN (Low Risk):${NC}" | tee -a "$REPORT_FILE"
    echo "✅ Log files older than 30 days" | tee -a "$REPORT_FILE"
    echo "✅ Cache files in /var/cache" | tee -a "$REPORT_FILE"
    echo "✅ Temporary files older than 7 days" | tee -a "$REPORT_FILE"
    echo "✅ Package cache (apt clean)" | tee -a "$REPORT_FILE"
    echo "✅ Compressed log files older than 60 days" | tee -a "$REPORT_FILE"
    echo "" | tee -a "$REPORT_FILE"
    
    echo -e "${YELLOW}MEDIUM RISK (Review Before Cleaning):${NC}" | tee -a "$REPORT_FILE"
    echo "⚠️  Old backup files (keep at least 3 recent)" | tee -a "$REPORT_FILE"
    echo "⚠️  MySQL binary logs (use PURGE BINARY LOGS)" | tee -a "$REPORT_FILE"
    echo "⚠️  Large media files in application directories" | tee -a "$REPORT_FILE"
    echo "" | tee -a "$REPORT_FILE"
    
    echo -e "${RED}NEVER DELETE (Critical):${NC}" | tee -a "$REPORT_FILE"
    echo "🚫 MySQL database files" | tee -a "$REPORT_FILE"
    echo "🚫 Application configuration files" | tee -a "$REPORT_FILE"
    echo "🚫 SSL certificates" | tee -a "$REPORT_FILE"
    echo "🚫 Recent database backups" | tee -a "$REPORT_FILE"
    echo "🚫 Application source code" | tee -a "$REPORT_FILE"
    echo "" | tee -a "$REPORT_FILE"
    
    echo -e "${BLUE}NEXT STEPS:${NC}" | tee -a "$REPORT_FILE"
    echo "1. Review this report carefully" | tee -a "$REPORT_FILE"
    echo "2. Run the companion cleanup script with --dry-run first" | tee -a "$REPORT_FILE"
    echo "3. Create manual backups of critical data before cleanup" | tee -a "$REPORT_FILE"
    echo "4. Monitor disk usage after cleanup" | tee -a "$REPORT_FILE"
    echo "5. Consider implementing automated log rotation" | tee -a "$REPORT_FILE"
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    print_header "$SCRIPT_NAME v$SCRIPT_VERSION"

    # Ensure report directory exists early to avoid tee errors
    if [[ ! -d "$REPORT_DIR" ]]; then
        mkdir -p "$REPORT_DIR" || {
            echo "Failed to create report directory: $REPORT_DIR" >&2
            exit 1
        }
        chmod 755 "$REPORT_DIR" || true
    fi

    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        echo -e "${YELLOW}Warning: Not running as root. Some analysis may be limited.${NC}"
        log_warn "Script not running as root - some analysis may be limited"
    fi
    
    setup_report_directory
    check_prerequisites
    
    log_info "Starting comprehensive disk space analysis..."
    
    # Run all analysis functions
    analyze_overall_disk_usage
    analyze_directory_sizes
    analyze_large_files
    analyze_log_files
    analyze_temporary_files
    generate_cleanup_recommendations
    
    # Final summary
    print_section "ANALYSIS COMPLETE"
    echo -e "${GREEN}✅ Disk space analysis complete!${NC}"
    echo -e "${BLUE}📊 Full report saved to: $REPORT_FILE${NC}"
    echo -e "${BLUE}📋 Review recommendations before proceeding with cleanup${NC}"
    
    log_success "Analysis completed successfully"
    
    # Display report location
    echo ""
    echo -e "${CYAN}Report Location: $REPORT_FILE${NC}"
    echo -e "${CYAN}Review the report and then run the cleanup script if needed.${NC}"
}

# Trap to ensure cleanup
trap 'echo "Analysis interrupted"; exit 1' INT TERM

# Run main function
main "$@"