#!/bin/bash

# Reverse Proxy Server Documentation Script
# Run this on your NGINX Reverse Proxy Server (*************)
# This script will generate comprehensive documentation of your reverse proxy setup

echo "=== REVERSE PROXY SERVER DOCUMENTATION GENERATOR ==="
echo "Starting documentation generation at $(date)"
echo "Server IP: $(curl -s ifconfig.me)"

# Create output directory
OUTPUT_DIR="/tmp/reverse-proxy-docs"
mkdir -p "$OUTPUT_DIR"

# Main documentation file
DOC_FILE="$OUTPUT_DIR/reverse-proxy-configuration.md"

cat > "$DOC_FILE" << 'EOF'
# Reverse Proxy Server Configuration Documentation

## Server Information
EOF

echo "## Server Details" >> "$DOC_FILE"
echo "- **Server IP:** $(curl -s ifconfig.me)" >> "$DOC_FILE"
echo "- **Hostname:** $(hostname)" >> "$DOC_FILE"
echo "- **OS:** $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)" >> "$DOC_FILE"
echo "- **Kernel:** $(uname -r)" >> "$DOC_FILE"
echo "- **Architecture:** $(uname -m)" >> "$DOC_FILE"
echo "- **Documentation Generated:** $(date)" >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# System Resources
echo "## System Resources" >> "$DOC_FILE"
echo "### CPU Information" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
lscpu >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### Memory Information" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
free -h >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### Disk Usage" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
df -h >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Network Configuration
echo "## Network Configuration" >> "$DOC_FILE"
echo "### Network Interfaces" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
ip addr show >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### Routing Table" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
ip route show >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### DNS Configuration" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
cat /etc/resolv.conf >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# NGINX Configuration
echo "## NGINX Configuration" >> "$DOC_FILE"
echo "### NGINX Version and Modules" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
nginx -V 2>&1 >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### NGINX Service Status" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
systemctl status nginx >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### NGINX Main Configuration" >> "$DOC_FILE"
echo '```nginx' >> "$DOC_FILE"
cat /etc/nginx/nginx.conf >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Site Configurations
echo "### NGINX Site Configurations" >> "$DOC_FILE"
for conf in /etc/nginx/sites-enabled/*; do
    if [ -f "$conf" ]; then
        echo "#### Configuration: $(basename $conf)" >> "$DOC_FILE"
        echo '```nginx' >> "$DOC_FILE"
        cat "$conf" >> "$DOC_FILE"
        echo '```' >> "$DOC_FILE"
        echo "" >> "$DOC_FILE"
    fi
done

# Additional NGINX configs
if [ -d "/etc/nginx/conf.d" ]; then
    echo "### Additional NGINX Configurations" >> "$DOC_FILE"
    for conf in /etc/nginx/conf.d/*.conf; do
        if [ -f "$conf" ]; then
            echo "#### Configuration: $(basename $conf)" >> "$DOC_FILE"
            echo '```nginx' >> "$DOC_FILE"
            cat "$conf" >> "$DOC_FILE"
            echo '```' >> "$DOC_FILE"
            echo "" >> "$DOC_FILE"
        fi
    done
fi

# SSL Certificates
echo "## SSL Certificates" >> "$DOC_FILE"
echo "### SSL Certificate Locations" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
find /etc/ssl /etc/letsencrypt /etc/nginx -name "*.crt" -o -name "*.pem" -o -name "*.key" 2>/dev/null >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Let's Encrypt if present
if [ -d "/etc/letsencrypt" ]; then
    echo "### Let's Encrypt Configuration" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    ls -la /etc/letsencrypt/live/ 2>/dev/null >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
fi

# Firewall Configuration
echo "## Firewall Configuration" >> "$DOC_FILE"
if command -v ufw &> /dev/null; then
    echo "### UFW Status" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    ufw status verbose >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
fi

if command -v iptables &> /dev/null; then
    echo "### IPTables Rules" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    iptables -L -n -v >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
fi

# Running Services
echo "## Running Services" >> "$DOC_FILE"
echo "### Active Services" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
systemctl list-units --type=service --state=active >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Port Usage
echo "### Open Ports" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
netstat -tulpn >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Log Files
echo "## Log File Locations" >> "$DOC_FILE"
echo "### NGINX Logs" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
ls -la /var/log/nginx/ >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### System Logs" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
ls -la /var/log/ | grep -E "(nginx|auth|syslog|messages)" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Cron Jobs
echo "## Scheduled Tasks" >> "$DOC_FILE"
echo "### Root Crontab" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
crontab -l 2>/dev/null || echo "No crontab for root" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Package Information
echo "## Installed Packages" >> "$DOC_FILE"
echo "### NGINX Related Packages" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
if command -v dpkg &> /dev/null; then
    dpkg -l | grep nginx >> "$DOC_FILE"
elif command -v rpm &> /dev/null; then
    rpm -qa | grep nginx >> "$DOC_FILE"
fi
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "Documentation generated successfully!"
echo "Output file: $DOC_FILE"
echo ""
echo "To download this file, you can use:"
echo "scp root@*************:$DOC_FILE ./reverse-proxy-config.md"
echo ""
echo "Or view it directly:"
echo "cat $DOC_FILE"
