#!/bin/bash

# ============================================================================
# StreamDB Production Server - Safe Disk Space Cleanup Script
# Server: backend1maindb (***********) - FastPanel CP with MySQL
# Purpose: Safe cleanup of temporary files, logs, and caches
# Author: Generated for streamdb.online
# Version: 1.0
# ============================================================================

set -euo pipefail

# Configuration
SCRIPT_NAME="StreamDB Safe Cleanup"
SCRIPT_VERSION="1.0"
CLEANUP_LOG_DIR="/var/log/streamdb-maintenance"
CLEANUP_LOG="$CLEANUP_LOG_DIR/disk-cleanup-$(date +%Y%m%d-%H%M%S).log"
BACKUP_DIR="/var/backups/streamdb-cleanup"
LOCK_FILE="/var/run/streamdb-cleanup.lock"

# Server-specific paths (FastPanel + StreamDB)
STREAMDB_PATH="/var/www/streamdb_root/data/www/streamdb.online"
FASTPANEL_PATH="/usr/local/mgr5"
MYSQL_DATA_DIR="/var/lib/mysql"
NGINX_LOG_DIR="/var/log/nginx"
MYSQL_LOG_DIR="/var/log/mysql"
PM2_LOG_DIR="$STREAMDB_PATH/logs"
MAINTENANCE_LOG_DIR="/var/log/streamdb-maintenance"

# Cleanup thresholds (in days)
LOG_RETENTION_DAYS=7
TEMP_FILE_RETENTION_DAYS=3
CACHE_RETENTION_DAYS=3
BACKUP_RETENTION_DAYS=30

# New flags
CLEAN_LOGS_ONLY=false
SETUP_LOGROTATE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Flags
DRY_RUN=false
FORCE_MODE=false
BACKUP_BEFORE_DELETE=true

# Paths to never modify (danger / review marked)
# These paths are protected and safe_delete will skip them.
DANGER_PATHS=(
    "$MYSQL_DATA_DIR"
    "$STREAMDB_PATH"
    "/var/www"
    "/etc"
    "/boot"
    "/usr/lib"
    "/usr/local"
)

REVIEW_PATHS=(
    "/var/upload"
    "/opt"
)

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Safe disk space cleanup for StreamDB production server.

OPTIONS:
    --dry-run           Show what would be cleaned without actually deleting
    --force             Skip interactive confirmations (use with caution)
    --no-backup         Skip creating backups before deletion
    --log-days N        Keep logs newer than N days (default: $LOG_RETENTION_DAYS)
    --temp-days N       Keep temp files newer than N days (default: $TEMP_FILE_RETENTION_DAYS)
    --help              Show this help message
    --clean-logs        Only run log cleanup routines (safe)
    --setup-logrotate   Install/configure log rotation for pm2 logs

EXAMPLES:
    $0 --dry-run                    # Preview cleanup actions
    $0 --log-days 60               # Keep 60 days of logs
    $0 --dry-run --log-days 14     # Preview cleaning logs older than 14 days

SAFETY FEATURES:
    - Dry run mode for preview
    - Automatic backups of critical files
    - Service health checks
    - Rollback capabilities
    - Exclusion of critical system files

EOF
}

setup_logrotate() {
    print_section "Configuring log rotation"

    # Try pm2-logrotate for PM2-managed applications
    if command -v pm2 >/dev/null; then
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[DRY RUN] Would install/configure pm2-logrotate"
        else
            log_info "Installing/configuring pm2-logrotate"
            pm2 install pm2-logrotate || log_warn "pm2-logrotate install failed or already installed"
            pm2 set pm2-logrotate:max_size 10M || true
            pm2 set pm2-logrotate:retain 30 || true
            pm2 set pm2-logrotate:compress true || true
            log_success "pm2-logrotate configured"
        fi
    else
        log_warn "pm2 not found - skipping pm2-logrotate install"
    fi

    # Create a system-level logrotate config for pm2 logs as a fallback
    local lr_file="/etc/logrotate.d/streamdb-pm2"
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would create logrotate file at $lr_file"
    else
        cat > "$lr_file" <<'LR'
/root/.pm2/logs/*.log {
    daily
    rotate 30
    compress
    missingok
    notifempty
    copytruncate
}
LR
        log_success "Created $lr_file"
    fi
}

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$CLEANUP_LOG"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }

print_header() {
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
    log_info "$1"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
    log_info "Starting: $1"
}

format_size() {
    local size_kb="$1"
    if (( size_kb >= 1048576 )); then
        printf "%.2f GB" "$(echo "scale=2; $size_kb / 1048576" | bc -l)"
    elif (( size_kb >= 1024 )); then
        printf "%.2f MB" "$(echo "scale=2; $size_kb / 1024" | bc -l)"
    else
        printf "%d KB" "$size_kb"
    fi
}

create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local pid=$(cat "$LOCK_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_error "Cleanup script is already running (PID: $pid)"
            exit 1
        else
            log_warn "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
    log_info "Created lock file: $LOCK_FILE"
}

remove_lock() {
    rm -f "$LOCK_FILE"
    log_info "Removed lock file"
}

setup_directories() {
    mkdir -p "$CLEANUP_LOG_DIR" "$BACKUP_DIR"
    chmod 755 "$CLEANUP_LOG_DIR" "$BACKUP_DIR"
    log_info "Created cleanup directories"
}

check_prerequisites() {
    local missing_tools=()
    
    # Check for required tools
    command -v find >/dev/null || missing_tools+=("find")
    command -v du >/dev/null || missing_tools+=("du")
    command -v tar >/dev/null || missing_tools+=("tar")
    command -v gzip >/dev/null || missing_tools+=("gzip")
    command -v bc >/dev/null || missing_tools+=("bc")
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        if [[ "$DRY_RUN" == "false" ]]; then
            log_info "Installing missing tools..."
            apt-get update && apt-get install -y "${missing_tools[@]}"
        fi
    fi
}

check_services() {
    print_section "Service Health Check"
    
    local services=("nginx" "mysql" "fastpanel2")
    local service_status=()
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            echo -e "${GREEN}✅ $service is running${NC}"
            log_info "Service $service is healthy"
            service_status+=("$service:active")
        else
            echo -e "${RED}❌ $service is not running${NC}"
            log_warn "Service $service is not running"
            service_status+=("$service:inactive")
        fi
    done
    
    # Check PM2 application
    if command -v pm2 >/dev/null && pm2 list | grep -q "streamdb-online.*online"; then
        echo -e "${GREEN}✅ PM2 StreamDB application is running${NC}"
        log_info "PM2 StreamDB application is healthy"
    else
        echo -e "${RED}❌ PM2 StreamDB application is not running${NC}"
        log_warn "PM2 StreamDB application is not running"
    fi
    
    # Store service states for restoration if needed
    printf '%s\n' "${service_status[@]}" > "$BACKUP_DIR/service-states-$(date +%Y%m%d-%H%M%S).txt"
}

create_backup() {
    local source_path="$1"
    local backup_name="$2"
    
    if [[ "$BACKUP_BEFORE_DELETE" == "true" && -e "$source_path" ]]; then
        local backup_file="$BACKUP_DIR/${backup_name}-$(date +%Y%m%d-%H%M%S).tar.gz"
        
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[DRY RUN] Would create backup: $backup_file"
        else
            log_info "Creating backup: $backup_file"
            tar -czf "$backup_file" -C "$(dirname "$source_path")" "$(basename "$source_path")" 2>/dev/null || {
                log_warn "Failed to create backup for $source_path"
                return 1
            }
            log_success "Backup created: $backup_file"
        fi
    fi
}

confirm_action() {
    local action="$1"
    
    if [[ "$FORCE_MODE" == "true" ]]; then
        return 0
    fi
    
    echo -e "${YELLOW}Confirm: $action${NC}"
    echo -n "Continue? [y/N]: "
    read -r response
    case "$response" in
        [yY]|[yY][eE][sS]) return 0 ;;
        *) return 1 ;;
    esac
}

safe_delete() {
    local target="$1"
    local description="$2"
    local backup_name="${3:-}"
    
    if [[ ! -e "$target" ]]; then
        log_info "Target does not exist: $target"
        return 0
    fi

    # Protect DANGER and REVIEW marked paths
    for p in "${DANGER_PATHS[@]}"; do
        if [[ "$target" == "$p"* || "$target" == "$p" ]]; then
            log_warn "Skipping protected DANGER path: $target"
            return 0
        fi
    done
    for p in "${REVIEW_PATHS[@]}"; do
        if [[ "$target" == "$p"* || "$target" == "$p" ]]; then
            log_warn "Skipping REVIEW path (requires manual review): $target"
            return 0
        fi
    done
    
    # Calculate size before deletion
    local size_kb=0
    if [[ -f "$target" ]]; then
        size_kb=$(du -k "$target" 2>/dev/null | cut -f1 || echo "0")
    elif [[ -d "$target" ]]; then
        size_kb=$(du -sk "$target" 2>/dev/null | cut -f1 || echo "0")
    fi
    
    local formatted_size=$(format_size $size_kb)
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${CYAN}[DRY RUN] Would delete: $target ($formatted_size) - $description${NC}"
        log_info "[DRY RUN] Would delete: $target ($formatted_size)"
        return 0
    fi
    
    # Create backup if requested and backup name provided
    if [[ -n "$backup_name" ]]; then
        create_backup "$target" "$backup_name"
    fi
    
    log_info "Deleting: $target ($formatted_size) - $description"
    
    if [[ -f "$target" ]]; then
        rm -f "$target" && log_success "Deleted file: $target ($formatted_size)"
    elif [[ -d "$target" ]]; then
        rm -rf "$target" && log_success "Deleted directory: $target ($formatted_size)"
    fi
}

# ============================================================================
# CLEANUP FUNCTIONS
# ============================================================================

cleanup_system_logs() {
    print_section "Cleaning System Log Files"
    
    if ! confirm_action "Clean system logs older than $LOG_RETENTION_DAYS days"; then
        log_info "Skipping system log cleanup"
        return 0
    fi
    
    local total_freed=0
    
    # Clean rotated log files
    echo -e "${CYAN}Cleaning rotated log files...${NC}"
    find /var/log -name "*.log.*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
        local size_kb=$(du -k "$logfile" 2>/dev/null | cut -f1 || echo "0")
        total_freed=$((total_freed + size_kb))
        safe_delete "$logfile" "Rotated log file"
    done
    
    # Clean compressed log files
    echo -e "${CYAN}Cleaning compressed log files...${NC}"
    find /var/log -name "*.gz" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r gzfile; do
        safe_delete "$gzfile" "Compressed log file"
    done
    
    # Clean old syslog files
    echo -e "${CYAN}Cleaning old syslog files...${NC}"
    find /var/log -name "syslog.*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r syslog; do
        safe_delete "$syslog" "Old syslog file"
    done
    
    log_success "System log cleanup completed"
}

cleanup_nginx_logs() {
    print_section "Cleaning Nginx Log Files"
    
    if [[ ! -d "$NGINX_LOG_DIR" ]]; then
        log_info "Nginx log directory not found: $NGINX_LOG_DIR"
        return 0
    fi
    
    if ! confirm_action "Clean Nginx logs older than $LOG_RETENTION_DAYS days"; then
        log_info "Skipping Nginx log cleanup"
        return 0
    fi
    
    echo -e "${CYAN}Cleaning Nginx access logs...${NC}"
    find "$NGINX_LOG_DIR" -name "access.log.*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
        safe_delete "$logfile" "Nginx access log"
    done
    
    echo -e "${CYAN}Cleaning Nginx error logs...${NC}"
    find "$NGINX_LOG_DIR" -name "error.log.*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
        safe_delete "$logfile" "Nginx error log"
    done
    
    # Clean StreamDB specific logs
    echo -e "${CYAN}Cleaning StreamDB specific logs...${NC}"
    find "$NGINX_LOG_DIR" -name "*streamdb*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
        safe_delete "$logfile" "StreamDB specific log"
    done
    
    log_success "Nginx log cleanup completed"
}

cleanup_mysql_logs() {
    print_section "Cleaning MySQL Log Files"
    
    if [[ ! -d "$MYSQL_LOG_DIR" ]]; then
        log_info "MySQL log directory not found: $MYSQL_LOG_DIR"
        return 0
    fi
    
    if ! confirm_action "Clean MySQL logs older than $LOG_RETENTION_DAYS days (EXCLUDING current error log)"; then
        log_info "Skipping MySQL log cleanup"
        return 0
    fi
    
    echo -e "${CYAN}Cleaning MySQL error logs...${NC}"
    find "$MYSQL_LOG_DIR" -name "error.log.*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
        safe_delete "$logfile" "MySQL error log"
    done
    
    echo -e "${CYAN}Cleaning MySQL slow query logs...${NC}"
    find "$MYSQL_LOG_DIR" -name "*slow.log*" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
        safe_delete "$logfile" "MySQL slow query log"
    done
    
    # Check for binary logs (more careful approach)
    if [[ -d "$MYSQL_DATA_DIR" ]]; then
        echo -e "${CYAN}Checking MySQL binary logs...${NC}"
        local binlog_count=$(find "$MYSQL_DATA_DIR" -name "mysql-bin.*" -type f 2>/dev/null | wc -l)
        if [[ $binlog_count -gt 10 ]]; then
            echo -e "${YELLOW}Found $binlog_count binary log files${NC}"
            echo -e "${YELLOW}Consider running: PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 7 DAY);${NC}"
            log_warn "Found $binlog_count binary log files - manual MySQL purge recommended"
        fi
    fi
    
    log_success "MySQL log cleanup completed"
}

cleanup_application_logs() {
    print_section "Cleaning Application Log Files"
    
    # Clean PM2 logs
    if [[ -d "$PM2_LOG_DIR" ]]; then
        if confirm_action "Clean PM2 application logs older than $LOG_RETENTION_DAYS days"; then
            echo -e "${CYAN}Cleaning PM2 logs...${NC}"
            find "$PM2_LOG_DIR" -name "*.log" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
                # Skip current active log files
                if [[ ! "$logfile" =~ (app-error\.log|app-out\.log|app-combined\.log|mysql-health-error\.log|mysql-health-out\.log|mysql-health-combined\.log)$ ]]; then
                    safe_delete "$logfile" "PM2 application log"
                fi
            done
        fi
    fi
    
    # Clean maintenance logs
    if [[ -d "$MAINTENANCE_LOG_DIR" ]]; then
        if confirm_action "Clean maintenance logs older than $LOG_RETENTION_DAYS days"; then
            echo -e "${CYAN}Cleaning maintenance logs...${NC}"
            find "$MAINTENANCE_LOG_DIR" -name "backend-maintenance-*.log" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
                safe_delete "$logfile" "Maintenance log"
            done
            
            find "$MAINTENANCE_LOG_DIR" -name "cron-*.log" -type f -mtime +$LOG_RETENTION_DAYS 2>/dev/null | while read -r logfile; do
                safe_delete "$logfile" "Cron log"
            done
        fi
    fi
    
    log_success "Application log cleanup completed"
}

cleanup_temporary_files() {
    print_section "Cleaning Temporary Files"
    
    if ! confirm_action "Clean temporary files older than $TEMP_FILE_RETENTION_DAYS days"; then
        log_info "Skipping temporary file cleanup"
        return 0
    fi
    
    # Clean /tmp
    echo -e "${CYAN}Cleaning /tmp directory...${NC}"
    find /tmp -type f -mtime +$TEMP_FILE_RETENTION_DAYS -not -path "*/systemd-*" -not -name "*.sql.gz" -not -name "*.tar.gz" 2>/dev/null | while read -r tempfile; do
        safe_delete "$tempfile" "Temporary file"
    done
    
    # Clean /var/tmp
    echo -e "${CYAN}Cleaning /var/tmp directory...${NC}"
    find /var/tmp -type f -mtime +$TEMP_FILE_RETENTION_DAYS 2>/dev/null | while read -r tempfile; do
        safe_delete "$tempfile" "Variable temporary file"
    done
    
    # Clean empty directories in /tmp
    if [[ "$DRY_RUN" == "false" ]]; then
        echo -e "${CYAN}Cleaning empty directories in /tmp...${NC}"
        find /tmp -type d -empty -mtime +$TEMP_FILE_RETENTION_DAYS 2>/dev/null | while read -r emptydir; do
            # Skip system directories
            if [[ ! "$emptydir" =~ ^/tmp/(systemd|ssh-|\.X11-unix|\.ICE-unix) ]]; then
                rmdir "$emptydir" 2>/dev/null || true
                log_info "Removed empty directory: $emptydir"
            fi
        done
    fi
    
    log_success "Temporary file cleanup completed"
}

cleanup_cache_files() {
    print_section "Cleaning Cache Files"
    
    if ! confirm_action "Clean cache files older than $CACHE_RETENTION_DAYS days"; then
        log_info "Skipping cache cleanup"
        return 0
    fi
    
    # Clean APT cache
    echo -e "${CYAN}Cleaning APT cache...${NC}"
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${CYAN}[DRY RUN] Would run: apt-get clean${NC}"
        log_info "[DRY RUN] Would clean APT cache"
    else
        apt-get clean && log_success "APT cache cleaned"
    fi
    
    # Clean package cache archives
    echo -e "${CYAN}Cleaning old package archives...${NC}"
    find /var/cache/apt/archives -name "*.deb" -type f -mtime +$CACHE_RETENTION_DAYS 2>/dev/null | while read -r debfile; do
        safe_delete "$debfile" "Package archive"
    done
    
    # Clean PHP sessions (if exists)
    if [[ -d "/var/lib/php/sessions" ]]; then
        echo -e "${CYAN}Cleaning PHP sessions...${NC}"
        find /var/lib/php/sessions -name "sess_*" -type f -mtime +$CACHE_RETENTION_DAYS 2>/dev/null | while read -r session; do
            safe_delete "$session" "PHP session file"
        done
    fi
    
    # Clean application cache (if exists)
    if [[ -d "$STREAMDB_PATH/cache" ]]; then
        echo -e "${CYAN}Cleaning application cache...${NC}"
        find "$STREAMDB_PATH/cache" -type f -mtime +$CACHE_RETENTION_DAYS 2>/dev/null | while read -r cachefile; do
            safe_delete "$cachefile" "Application cache file"
        done
    fi
    
    log_success "Cache cleanup completed"
}

cleanup_old_backups() {
    print_section "Cleaning Old Backup Files"
    
    if ! confirm_action "Clean backup files older than $BACKUP_RETENTION_DAYS days"; then
        log_info "Skipping backup cleanup"
        return 0
    fi
    
    # NOTE: This is conservative - only removes very old backups
    local backup_locations=(
        "/var/backups"
        "$BACKUP_DIR"
        "/tmp"
    )
    
    for backup_location in "${backup_locations[@]}"; do
        if [[ -d "$backup_location" ]]; then
            echo -e "${CYAN}Cleaning old backups in $backup_location...${NC}"
            
            # Only remove backup files that are clearly temporary or very old
            find "$backup_location" -name "*.tar.gz" -type f -mtime +$BACKUP_RETENTION_DAYS 2>/dev/null | while read -r backupfile; do
                # Skip database backups and configuration backups (keep forever)
                if [[ ! "$backupfile" =~ (mysql-backup|config-backup) ]]; then
                    safe_delete "$backupfile" "Old backup file"
                fi
            done
        fi
    done
    
    log_success "Backup cleanup completed"
}

generate_cleanup_summary() {
    print_section "CLEANUP SUMMARY"
    
    echo -e "${PURPLE}===== CLEANUP COMPLETED =====${NC}"
    echo "" | tee -a "$CLEANUP_LOG"
    
    # Calculate total space freed (this is approximate)
    local total_freed_mb=0
    
    echo -e "${GREEN}CLEANUP ACTIONS COMPLETED:${NC}" | tee -a "$CLEANUP_LOG"
    echo "✅ System log files cleanup" | tee -a "$CLEANUP_LOG"
    echo "✅ Nginx log files cleanup" | tee -a "$CLEANUP_LOG"
    echo "✅ MySQL log files cleanup" | tee -a "$CLEANUP_LOG"
    echo "✅ Application log files cleanup" | tee -a "$CLEANUP_LOG"
    echo "✅ Temporary files cleanup" | tee -a "$CLEANUP_LOG"
    echo "✅ Cache files cleanup" | tee -a "$CLEANUP_LOG"
    echo "✅ Old backup files cleanup" | tee -a "$CLEANUP_LOG"
    echo "" | tee -a "$CLEANUP_LOG"
    
    echo -e "${BLUE}POST-CLEANUP RECOMMENDATIONS:${NC}" | tee -a "$CLEANUP_LOG"
    echo "1. Monitor disk usage over the next few days" | tee -a "$CLEANUP_LOG"
    echo "2. Verify all services are running correctly" | tee -a "$CLEANUP_LOG"
    echo "3. Check application functionality" | tee -a "$CLEANUP_LOG"
    echo "4. Consider implementing log rotation if not already configured" | tee -a "$CLEANUP_LOG"
    echo "5. Schedule regular maintenance to prevent future disk space issues" | tee -a "$CLEANUP_LOG"
    echo "" | tee -a "$CLEANUP_LOG"
    
    echo -e "${CYAN}Cleanup log saved to: $CLEANUP_LOG${NC}"
    echo -e "${CYAN}Backups created in: $BACKUP_DIR${NC}"
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --clean-logs)
                CLEAN_LOGS_ONLY=true
                shift
                ;;
            --setup-logrotate)
                SETUP_LOGROTATE=true
                shift
                ;;
            --force)
                FORCE_MODE=true
                shift
                ;;
            --no-backup)
                BACKUP_BEFORE_DELETE=false
                shift
                ;;
            --log-days)
                LOG_RETENTION_DAYS="$2"
                shift 2
                ;;
            --temp-days)
                TEMP_FILE_RETENTION_DAYS="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
}

main() {
    parse_arguments "$@"
    
    print_header "$SCRIPT_NAME v$SCRIPT_VERSION"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${YELLOW}🔍 DRY RUN MODE - No files will be deleted${NC}"
        log_info "Running in DRY RUN mode"
    fi
    
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root for full functionality"
        exit 1
    fi
    
    create_lock
    trap remove_lock EXIT
    
    setup_directories
    check_prerequisites
    check_services

    # Optional: setup log rotation before cleaning
    if [[ "$SETUP_LOGROTATE" == "true" ]]; then
        setup_logrotate
        # If user only wanted setup, exit after configuring
        if [[ "$CLEAN_LOGS_ONLY" == "false" && "$DRY_RUN" == "false" ]]; then
            log_info "Logrotate setup completed"
        fi
    fi
    
    log_info "Starting safe disk cleanup process..."
    log_info "Log retention: $LOG_RETENTION_DAYS days"
    log_info "Temp file retention: $TEMP_FILE_RETENTION_DAYS days"
    log_info "Cache retention: $CACHE_RETENTION_DAYS days"
    
    # Display current disk usage
    echo -e "${CYAN}Current disk usage:${NC}"
    df -h / | tail -1
    
    # Run cleanup functions
    if [[ "$CLEAN_LOGS_ONLY" == "true" ]]; then
        cleanup_system_logs
        cleanup_nginx_logs
        cleanup_mysql_logs
        cleanup_application_logs
    else
        cleanup_system_logs
        cleanup_nginx_logs
        cleanup_mysql_logs
        cleanup_application_logs
        cleanup_temporary_files
        cleanup_cache_files
        cleanup_old_backups
    fi
    
    # Final service check
    check_services
    
    # Generate summary
    generate_cleanup_summary
    
    # Display final disk usage
    echo -e "${CYAN}Disk usage after cleanup:${NC}"
    df -h / | tail -1
    
    log_success "Safe cleanup process completed successfully"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${YELLOW}🔍 This was a DRY RUN - no files were actually deleted${NC}"
        echo -e "${YELLOW}Run without --dry-run to perform actual cleanup${NC}"
    fi
}

# Trap to ensure cleanup
trap 'remove_lock; echo "Cleanup interrupted"; exit 1' INT TERM

# Run main function
main "$@"