# StreamDB Production Server - Disk Space Management Scripts

## Overview

This document provides comprehensive guidance for managing disk space on your StreamDB production backend server (***********) running FastPanel CP with MySQL. Two scripts have been created to safely analyze and clean up disk space while maintaining system integrity.

## Scripts Included

### 1. `disk-space-analyzer.sh` - Analysis & Report Script
- **Purpose**: Comprehensive disk usage analysis and cleanup recommendations
- **Safety**: Read-only analysis, no modifications to the system
- **Output**: Detailed report with recommendations and safety ratings

### 2. `disk-space-cleanup.sh` - Safe Cleanup Execution Script
- **Purpose**: Safe cleanup of temporary files, logs, and caches
- **Safety**: Multiple safeguards, dry-run mode, automatic backups
- **Output**: Detailed cleanup log and space freed summary

## System Requirements

- **Server**: backend1maindb (***********)
- **OS**: Linux with FastPanel CP
- **Services**: MySQL, Nginx, PM2, StreamDB application
- **Access**: Root privileges required for full functionality
- **Tools**: bash, find, du, df, bc, tar, gzip (auto-installed if missing)

## Installation & Setup

### 1. Upload Scripts to Server

```bash
# Upload scripts to server
scp disk-space-analyzer.sh disk-space-cleanup.sh root@***********:/root/

# SSH into server
ssh root@***********

# Make scripts executable
chmod +x /root/disk-space-analyzer.sh
chmod +x /root/disk-space-cleanup.sh
```

# Verify services are running
systemctl status nginx mysql fastpanel2
pm2 list


# Check StreamDB application status
cd /var/www/streamdb_root/data/www/streamdb.online
pm2 show streamdb-online
```

### 2. Verify Server Environment

```bash
# Check current disk usage
df -h

# Verify services are running
```bash
# Run comprehensive analysis (safe, read-only)
./disk-space-analyzer.sh

# View the generated report
cat /tmp/streamdb-disk-analysis/disk-analysis-*.txt
```

## Usage Guide

### Step 1: Run Disk Space Analysis (ALWAYS FIRST)

```
--log-days N        # Keep logs newer than N days (default: 7)
--temp-days N       # Keep temp files newer than N days (default: 3)
- ✅ Analyzes overall disk usage and filesystem health
- ✅ Identifies largest directories and files
### Default Retention Periods

- **Log files**: 7 days (configurable with `--log-days`)
- **Temporary files**: 3 days (configurable with `--temp-days`)
- **Cache files**: 3 days (fixed)
- **Backup files**: 30 days (only removes non-critical backups)
- ✅ Provides specific cleanup recommendations
- ✅ Generates detailed report with safety ratings

### Step 2: Preview Cleanup Actions (DRY RUN)

```bash
# ALWAYS run dry-run first to preview actions
./disk-space-cleanup.sh --dry-run

# Preview with custom retention periods
./disk-space-cleanup.sh --dry-run --log-days 14 --temp-days 3
```

**What dry-run shows:**
- 🔍 Files that would be deleted (no actual deletion)
- 📊 Amount of space that would be freed
- 🛡️ Safety checks and service verifications
- 📋 Detailed action plan

### Step 3: Execute Safe Cleanup
/var/www/streamdb_root/data/www/streamdb.online/logs/*.log  # Old PM2 logs
```bash
# Run cleanup with interactive confirmations
./disk-space-cleanup.sh

# Run cleanup with custom settings
./disk-space-cleanup.sh --log-days 30 --temp-days 7

# Run cleanup without confirmations (use carefully)
./disk-space-cleanup.sh --force --log-days 60
```

# Weekly disk space analysis (Sundays at 2 AM)

### Disk Space Analyzer Options

```bash
./disk-space-analyzer.sh [no options - always safe]
```

### Disk Space Cleanup Options

```bash

#### 3. Analyzer 'tee' No such file or directory error

If you see output like:

```
tee: /tmp/streamdb-disk-analysis/disk-analysis-20250902-171923.txt: No such file or directory
cat: '/tmp/streamdb-disk-analysis/disk-analysis-*.txt': No such file or directory
```

This means the analyzer attempted to write to a report file but the report directory did not exist yet. The analyzer script has been updated (v1.1) to create `/tmp/streamdb-disk-analysis` early during execution and set permissions. If you still see the error, ensure `/tmp` is writable and create the directory manually:

```bash
mkdir -p /tmp/streamdb-disk-analysis
chmod 755 /tmp/streamdb-disk-analysis
```

Then re-run `./disk-space-analyzer.sh`.
./disk-space-cleanup.sh [OPTIONS]

--dry-run           # Preview actions without deleting (RECOMMENDED FIRST)
--force             # Skip interactive confirmations (use with caution)
--no-backup         # Skip creating backups before deletion
--log-days N        # Keep logs newer than N days (default: 30)
--temp-days N       # Keep temp files newer than N days (default: 7)
--help              # Show help message
```

### Default Retention Periods

- **Log files**: 30 days (configurable with `--log-days`)
- **Temporary files**: 7 days (configurable with `--temp-days`)
- **Cache files**: 3 days (fixed)
- **Backup files**: 90 days (only removes non-critical backups)

## Safety Features

### Built-in Protection

#### Never Deleted (Protected Files):
- 🚫 MySQL database files (`/var/lib/mysql/*`)
- 🚫 Application source code (`/var/www/streamdb_onl_usr/data/www/streamdb.online/*`)
- 🚫 Configuration files (nginx, mysql, fastpanel)
- 🚫 SSL certificates and keys
- 🚫 Recent database backups (`mysql-backup-*.sql.gz`)
- 🚫 Configuration backups (`config-backup-*.tar.gz`)
- 🚫 Active log files (current application logs)
- 🚫 System directories and binaries

#### Safe to Clean (Automated):
- ✅ Rotated log files older than retention period
- ✅ Compressed log files (`.gz`, `.log.*`)
- ✅ Temporary files in `/tmp` and `/var/tmp`
- ✅ Package cache (`apt` cache)
- ✅ PHP session files
- ✅ Application cache files
- ✅ Old maintenance logs

#### Medium Risk (Manual Review):
- ⚠️ Large backup files (older than 90 days)
- ⚠️ MySQL binary logs (requires `PURGE BINARY LOGS`)
- ⚠️ Large media files in application directories

### Backup & Recovery

#### Automatic Backups:
- Configuration snapshots before major cleanups
- Service state preservation
- Critical file backups (when applicable)
- Rollback capability for configuration changes

#### Backup Locations:
```bash
/var/backups/streamdb-cleanup/     # Cleanup-related backups
/var/log/streamdb-maintenance/     # Cleanup logs
/tmp/streamdb-disk-analysis/       # Analysis reports
```

### Service Protection

#### Pre-Cleanup Checks:
- ✅ Verify nginx is running
- ✅ Verify mysql is running  
- ✅ Verify fastpanel2 is running
- ✅ Verify PM2 StreamDB application is running
- ✅ Check database connectivity

#### Post-Cleanup Verification:
- ✅ Re-verify all services are still running
- ✅ Test database connectivity
- ✅ Validate application responsiveness

## What Gets Cleaned

### System Logs
```bash
/var/log/*.log.*           # Rotated system logs
/var/log/*.gz              # Compressed logs
/var/log/syslog.*          # Old syslog files
```

### Web Server Logs
```bash
/var/log/nginx/access.log.*     # Nginx access logs
/var/log/nginx/error.log.*      # Nginx error logs
/var/log/nginx/*streamdb*       # StreamDB specific logs
```

### Database Logs
```bash
/var/log/mysql/error.log.*      # MySQL error logs
/var/log/mysql/*slow.log*       # Slow query logs
# Note: Binary logs require manual MySQL PURGE command
```

### Application Logs
```bash
/var/www/streamdb_onl_usr/data/www/streamdb.online/logs/*.log  # Old PM2 logs
/var/log/streamdb-maintenance/backend-maintenance-*.log        # Maintenance logs
/var/log/streamdb-maintenance/cron-*.log                      # Cron logs
```

### Temporary Files
```bash
/tmp/*                    # Temporary files (excluding system & backups)
/var/tmp/*               # Variable temporary files
/var/cache/apt/archives/* # Package archives
/var/lib/php/sessions/*   # PHP session files
```

## Monitoring & Maintenance

### Regular Monitoring

```bash
# Check disk usage
df -h

# Monitor largest directories
du -sh /var/log /tmp /var/cache /var/lib/mysql

# Check service status
systemctl status nginx mysql fastpanel2
pm2 status
```

### Scheduled Maintenance

Consider adding to crontab for regular cleanup:

```bash
# Weekly disk space analysis (Sundays at 2 AM)
0 2 * * 0 /root/disk-space-analyzer.sh > /var/log/weekly-disk-analysis.log 2>&1

# Monthly safe cleanup (First Sunday of month at 3 AM)
0 3 1-7 * 0 [ "$(date +\%u)" = "7" ] && /root/disk-space-cleanup.sh --force --log-days 60 > /var/log/monthly-cleanup.log 2>&1
```

### Log Rotation Setup

Ensure logrotate is configured for application logs:

```bash
# Create logrotate config for StreamDB
cat > /etc/logrotate.d/streamdb << EOF
/var/www/streamdb_onl_usr/data/www/streamdb.online/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

## Troubleshooting

### Common Issues

#### 1. Script Permission Denied
```bash
chmod +x /root/disk-space-analyzer.sh
chmod +x /root/disk-space-cleanup.sh
```

#### 2. Missing Tools
Scripts will auto-install missing tools, or manually install:
```bash
apt-get update
apt-get install -y bc findutils coreutils tar gzip
```

#### 3. Service Stopped After Cleanup
Check service status and restart if needed:
```bash
systemctl status nginx mysql fastpanel2
systemctl restart nginx  # If needed
pm2 restart streamdb-online  # If needed
```

#### 4. MySQL Binary Logs Growing
Manual cleanup required:
```bash
mysql -u root -p
PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 7 DAY);
```

### Emergency Recovery

If cleanup causes issues:

#### 1. Restore from Backup
```bash
# Check available backups
ls -la /var/backups/streamdb-cleanup/

# Restore configuration backup
tar -xzf /var/backups/streamdb-cleanup/config-backup-*.tar.gz -C /
```

#### 2. Restart Services
```bash
systemctl restart nginx mysql fastpanel2
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
pm2 restart streamdb-online
```

#### 3. Check Application
```bash
# Test website
curl -I http://localhost:3001

# Check database connectivity
mysql -u stream_db_admin -p stream_db -e "SELECT 1;"
```

## Performance Impact

### During Analysis
- **Impact**: Minimal (read-only operations)
- **Duration**: 2-10 minutes depending on disk size
- **Services**: No interruption

### During Cleanup
- **Impact**: Low (file operations only)
- **Duration**: 5-30 minutes depending on cleanup size
- **Services**: Brief pauses during log rotation, no downtime

### Best Practices
- Run during low-traffic periods
- Monitor system resources during execution
- Verify services after completion
- Keep multiple generations of backups

## Security Considerations

### Access Control
- Scripts require root privileges
- Store scripts in secure location (`/root/`)
- Protect cleanup logs from unauthorized access
- Review backup file permissions

### Audit Trail
- All actions logged with timestamps
- Backup creation tracked
- Service state changes recorded
- Cleanup summary generated

## Contact & Support

- **Generated for**: streamdb.online
- **Server**: backend1maindb (***********)
- **Documentation**: Keep this file with scripts
- **Backup Location**: Store copies in project documentation

## Version History

- **v1.0**: Initial release with comprehensive analysis and safe cleanup
- **Features**: Dry-run mode, automatic backups, service protection, detailed logging

---

**⚠️ IMPORTANT REMINDERS:**

1. **ALWAYS run analysis first** to understand what will be cleaned
2. **ALWAYS use --dry-run** before actual cleanup
3. **Monitor services** after cleanup completion
4. **Keep recent backups** of critical data
5. **Test application functionality** after major cleanups
6. **Review logs** for any error messages

**🛡️ SAFETY FIRST:** These scripts are designed to be conservative. When in doubt, don't delete files manually - rely on the script's safety mechanisms.