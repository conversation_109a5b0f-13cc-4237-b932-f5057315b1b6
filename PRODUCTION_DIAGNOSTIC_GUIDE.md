# 🔍 MANUAL PRODUCTION DIAGNOSTIC TESTS
# Safe, Read-Only Tests for Social Sharing Issue

## ✅ SAFETY GUARANTEE
These tests ONLY READ data - they make NO changes to your system!

---

## 📋 Step 1: Find Real Published Content IDs

```bash
# Connect to your MySQL database and run this READ-ONLY query:
mysql -u root -p stream_db

# Then run this SELECT query (safe, read-only):
SELECT id, title, is_published, poster_url 
FROM content 
WHERE is_published = 1 
LIMIT 5;

# Note down at least one content ID from the results
# Example: content_1234567890_abcdef123
```

---

## 📋 Step 2: Test Debug Endpoint with Real Content

```bash
# Replace REAL_CONTENT_ID with an ID from Step 1:
curl "https://streamdb.online/api/debug/meta/REAL_CONTENT_ID"

# Expected: Should return content details, not "Content not found"
```

---

## 📋 Step 3: Test Social Sharing Detection

```bash
# Replace with your actual content URL:
curl -A "facebookexternalhit/1.1" "https://streamdb.online/content/your-actual-content-url" > /tmp/social_test.html

# Check if it contains content-specific meta tags:
grep -E "og:title|og:image|og:description" /tmp/social_test.html
```

---

## 📋 Step 4: Check Server Logs

```bash
# Make a request to trigger social sharing detection:
curl -A "facebookexternalhit/1.1" "https://streamdb.online/content/your-actual-content-url" > /dev/null

# Then check server logs for detection:
pm2 logs streamdb-online --lines 10 | grep -E "Social sharing|Content found|Generated meta"
```

---

## 📋 Step 5: Verify Content Access

```bash
# Test that regular content access still works:
curl -s "https://streamdb.online/content/your-actual-content-url" | head -10

# Should return HTML content normally
```

---

## 🎯 WHAT TO LOOK FOR:

### ✅ SUCCESS INDICATORS:
- Step 1: Shows actual content IDs that exist in database
- Step 2: Debug endpoint returns content data (not "Content not found")  
- Step 3: HTML contains content-specific og:title, og:image
- Step 4: Server logs show "🔍 Social sharing request detected"
- Step 5: Regular content access works normally

### ❌ PROBLEM INDICATORS:
- Step 1: No published content found
- Step 2: "Content not found" error
- Step 3: Generic StreamDB meta tags instead of content-specific
- Step 4: No social sharing detection in logs
- Step 5: Content pages don't load

---

## 🚨 SAFETY NOTES:
- ✅ All commands are READ-ONLY
- ✅ No database modifications
- ✅ No file changes
- ✅ No server configuration changes
- ✅ Safe to run in production

---

## 📊 REPORTING RESULTS:
After running tests, report:
1. What content IDs exist in your database
2. What the debug endpoint returns  
3. Whether social sharing detection appears in logs
4. Whether content-specific meta tags are generated