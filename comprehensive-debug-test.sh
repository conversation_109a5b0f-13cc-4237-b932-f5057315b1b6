#!/bin/bash
# COMPREHENSIVE DEBUG TEST FOR SOCIAL SHARING

echo "🔧 COMPREHENSIVE SOCIAL SHARING DEBUG TEST"
echo "=========================================="
echo "📅 $(date)"
echo "🎯 Target URL: https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"
echo ""

echo "STEP 1: VERIFY DEBUG ENDPOINT IS WORKING"
echo "========================================"
echo "🔍 Testing debug endpoint..."
DEBUG_RESULT=$(curl -s "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx")
echo "📊 Debug endpoint result:"
echo "$DEBUG_RESULT" | head -10
echo ""

if echo "$DEBUG_RESULT" | grep -q '"success":true'; then
    echo "✅ Debug endpoint is working correctly"
    echo "📋 Content found and meta tags generated"
else
    echo "❌ Debug endpoint failed - stopping test"
    exit 1
fi

echo ""
echo "STEP 2: TEST SOCIAL SHARING WITH FACEBOOK USER AGENT"
echo "=================================================="
echo "🔍 Making request with Facebook crawler user agent..."
echo "📡 URL: https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"

# Make request and save response
curl -s -A "facebookexternalhit/1.1" "https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx" > /tmp/social_sharing_test.html

echo ""
echo "📊 ANALYZING RESPONSE:"
echo "===================="

# Check response size
RESPONSE_SIZE=$(wc -c < /tmp/social_sharing_test.html)
echo "📏 Response size: $RESPONSE_SIZE bytes"

# Check for HTML content
if grep -q "<html" /tmp/social_sharing_test.html; then
    echo "✅ Valid HTML response received"
else
    echo "❌ Invalid response - not HTML"
    echo "📋 First 200 characters:"
    head -c 200 /tmp/social_sharing_test.html
    exit 1
fi

echo ""
echo "🏷️ META TAG ANALYSIS:"
echo "===================="

echo "📋 Page Title:"
if grep -q "<title>" /tmp/social_sharing_test.html; then
    TITLE=$(grep -o '<title>[^<]*</title>' /tmp/social_sharing_test.html)
    echo "Found: $TITLE"
    
    if echo "$TITLE" | grep -q "Mahavatar Narsimha"; then
        echo "✅ Content-specific title found"
    else
        echo "❌ Generic title found"
    fi
else
    echo "❌ No title tag found"
fi

echo ""
echo "📋 OG Image:"
if grep -q "og:image" /tmp/social_sharing_test.html; then
    OG_IMAGE=$(grep -o 'og:image[^>]*content="[^"]*"' /tmp/social_sharing_test.html | head -1)
    echo "Found: $OG_IMAGE"
    
    if echo "$OG_IMAGE" | grep -q "wfGTnnP35bWCd8iOtmKa2Hy7MAQ.jpg"; then
        echo "✅ Content-specific poster found"
    elif echo "$OG_IMAGE" | grep -q "android-chrome"; then
        echo "❌ Generic website icon found"
    else
        echo "❓ Unknown image found"
    fi
else
    echo "❌ No OG image found"
fi

echo ""
echo "📋 OG Title:"
if grep -q "og:title" /tmp/social_sharing_test.html; then
    OG_TITLE=$(grep -o 'og:title[^>]*content="[^"]*"' /tmp/social_sharing_test.html)
    echo "Found: $OG_TITLE"
    
    if echo "$OG_TITLE" | grep -q "Mahavatar Narsimha"; then
        echo "✅ Content-specific OG title found"
    else
        echo "❌ Generic OG title found"
    fi
else
    echo "❌ No OG title found"
fi

echo ""
echo "STEP 3: CHECK SERVER LOGS FOR DEBUG OUTPUT"
echo "=========================================="
echo "🔍 Checking server logs after the request..."

# Wait a moment for logs to appear
sleep 2

echo "📊 Recent server logs:"
if command -v pm2 &> /dev/null; then
    pm2 logs streamdb-online --lines 20 | tail -15
else
    echo "❌ PM2 not available to check logs"
fi

echo ""
echo "STEP 4: MANUAL VERIFICATION STEPS"
echo "================================="
echo "🔍 To manually verify the issue:"
echo ""
echo "1. Check Facebook Sharing Debugger:"
echo "   https://developers.facebook.com/tools/debug/"
echo "   Enter URL: https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx"
echo ""
echo "2. Test in WhatsApp:"
echo "   Share the URL and check if poster appears"
echo ""
echo "3. Check production server logs:"
echo "   pm2 logs streamdb-online --lines 50"

# Cleanup
rm -f /tmp/social_sharing_test.html

echo ""
echo "✅ COMPREHENSIVE DEBUG TEST COMPLETED"
echo "===================================="