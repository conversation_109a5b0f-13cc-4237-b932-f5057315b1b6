#!/bin/bash
# Quick test script to verify the database fix

echo "🔧 TESTING DATABASE FIX"
echo "======================="
echo "📅 Test Date: $(date)"
echo ""

echo "1️⃣ Testing debug endpoint with published content filter..."
echo "------------------------------------------------------"
curl -s "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx" | head -10
echo ""
echo ""

echo "2️⃣ Testing social sharing detection..."
echo "------------------------------------"
echo "🔍 Making request with Facebook crawler user agent..."
curl -s -A "facebookexternalhit/1.1" "https://streamdb.online/content/mahavatar-narsimha-hin-tel-content_1754261671794_3sztrufdx" > /tmp/test_fix.html

echo "📋 Checking for content-specific meta tags..."
if grep -q "Mahavatar Narsimha" /tmp/test_fix.html; then
    echo "✅ SUCCESS: Found content-specific title!"
    echo "🏷️ Title found:"
    grep "<title>" /tmp/test_fix.html
    echo "🖼️ OG Image:"
    grep "og:image" /tmp/test_fix.html | grep -v "android-chrome"
else
    echo "❌ ISSUE: Still showing generic content"
    echo "🏷️ Current title:"
    grep "<title>" /tmp/test_fix.html
fi

echo ""
echo "3️⃣ Checking server logs for successful detection..."
echo "------------------------------------------------"
pm2 logs streamdb-online --lines 5 | grep -E "Social sharing|Content found|Generated meta"

echo ""
echo "✅ TEST COMPLETED"
echo "=================="
echo "🚨 If you see content-specific titles and images above, the fix worked!"
echo "🚨 If you still see generic StreamDB content, restart your server:"
echo "   pm2 restart streamdb-online"

# Cleanup
rm -f /tmp/test_fix.html