# 🚨 PRODUCTION DEPLOYMENT TROUBLESHOOTING GUIDE

## MOST LIKELY ISSUES AFTER FILE UPLOAD & RESTART

Based on your StreamDB infrastructure (Ubuntu 24.04, Node.js v20.19.3, MySQL 8.0.42, PM2), here are the most probable causes:

---

## 🔍 **ISSUE 1: DATABASE RESULT DESTRUCTURING** (MOST LIKELY)

**Problem**: Different MySQL2 driver behavior in production vs development

**Root Cause**: The debug endpoint is logging "Query returned undefined rows" which suggests the database query is succeeding but result destructuring is failing.

**Current Code**:
```javascript
const [contentRows] = await db.execute(
  'SELECT id, title, description, year, type, poster_url, image, is_published FROM content WHERE id = ? AND is_published = 1 LIMIT 1',
  [contentId]
);
```

**Fix Needed**: Handle different result formats
```javascript
const result = await db.execute(
  'SELECT id, title, description, year, type, poster_url, image, is_published FROM content WHERE id = ? AND is_published = 1 LIMIT 1',
  [contentId]
);

// Handle both result formats
let contentRows;
if (Array.isArray(result) && Array.isArray(result[0])) {
  contentRows = result[0]; // [rows, fields] format
} else {
  contentRows = result; // rows only format
}
```

---

## 🔍 **ISSUE 2: ENVIRONMENT VARIABLES** (SECOND MOST LIKELY)

**Problem**: Database connection parameters not set correctly in production

**Check Required Variables**:
```bash
# On production server:
echo "DB_USER: $DB_USER"
echo "DB_PASSWORD: $DB_PASSWORD" 
echo "DB_NAME: $DB_NAME"
echo "DB_SOCKET: $DB_SOCKET"
echo "NODE_ENV: $NODE_ENV"
```

**Expected Values**:
- `DB_SOCKET=/var/run/mysqld/mysqld.sock`
- `NODE_ENV=production`
- `DB_NAME=stream_db` (or similar)

---

## 🔍 **ISSUE 3: FILE PATH RESOLUTION**

**Problem**: `dist/index.html` file not found

**Check**: 
```bash
# On production server:
ls -la /var/www/streamdb_root/data/www/streamdb.online/dist/index.html
```

**Fix if Missing**: Ensure the React build is deployed to the correct location

---

## 🔍 **ISSUE 4: PM2 ENVIRONMENT LOADING**

**Problem**: PM2 not loading environment variables properly

**Fix**:
```bash
# On production server:
pm2 stop streamdb-online
pm2 delete streamdb-online
cd /var/www/streamdb_root/data/www/streamdb.online
pm2 start server/index.js --name streamdb-online --env production
```

---

## 🎯 **IMMEDIATE ACTION PLAN**

### Step 1: Quick Database Test
Run this on production server:
```bash
# Test database connection directly
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "SELECT id, title, is_published FROM content WHERE id = 'content_1754261671794_3sztrufdx';"
```

### Step 2: Check Server Logs
```bash
# Check for specific errors
pm2 logs streamdb-online --lines 50 | grep -E "Error|undefined|null|cannot"
```

### Step 3: Test Debug Endpoint Manually
```bash
# Make request and check logs immediately
curl -s "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx"
pm2 logs streamdb-online --lines 5
```

---

## 🔧 **TARGETED FIXES**

### Fix 1: Update Database Query Handling (Most Likely Needed)
If database results show "undefined rows", update the debug endpoint to handle result formats properly.

### Fix 2: Environment Variables
Ensure `.env` file exists and is properly loaded by PM2.

### Fix 3: File Permissions
```bash
# Fix file permissions if needed
chmod 644 /var/www/streamdb_root/data/www/streamdb.online/server/index.js
chmod -R 755 /var/www/streamdb_root/data/www/streamdb.online/dist/
```

---

## 📊 **DIAGNOSTIC COMMANDS FOR PRODUCTION**

Run these in order to identify the exact issue:

```bash
# 1. Basic connectivity
curl -s "https://streamdb.online/api/health"

# 2. Database test
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "SELECT COUNT(*) FROM content WHERE is_published = 1;"

# 3. Debug endpoint test
curl -s "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx"

# 4. Check server logs
pm2 logs streamdb-online --lines 20

# 5. Environment check
env | grep -E "DB_|NODE_"
```

The most likely issue is **database result destructuring** based on the "undefined rows" message in your earlier test.