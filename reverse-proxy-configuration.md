# Reverse Proxy Server Configuration Documentation

## Server Information
## Server Details
- **Server IP:** *************
- **Hostname:** backend2ndrevproxy
- **OS:** Ubuntu 24.04.2 LTS
- **Kernel:** 6.8.0-31-generic
- **Architecture:** x86_64
- **Documentation Generated:** Mon Jul 21 15:07:51 UTC 2025

## System Resources
### CPU Information
```
Architecture:                         x86_64
CPU op-mode(s):                       32-bit, 64-bit
Address sizes:                        40 bits physical, 48 bits virtual
Byte Order:                           Little Endian
CPU(s):                               1
On-line CPU(s) list:                  0
Vendor ID:                            GenuineIntel
BIOS Vendor ID:                       QEMU
Model name:                           QEMU Virtual CPU version 2.5+
BIOS Model name:                      pc-i440fx-9.0  CPU @ 2.0GHz
BIOS CPU family:                      1
CPU family:                           15
Model:                                107
Thread(s) per core:                   1
Core(s) per socket:                   1
Socket(s):                            1
Stepping:                             1
BogoMIPS:                             4199.99
Flags:                                fpu de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 syscall nx lm constant_tsc nopl xtopology cpuid tsc_known_freq pni ssse3 cx16 sse4_1 sse4_2 x2apic popcnt aes hypervisor lahf_lm cpuid_fault pti
Hypervisor vendor:                    KVM
Virtualization type:                  full
L1d cache:                            32 KiB (1 instance)
L1i cache:                            32 KiB (1 instance)
L2 cache:                             4 MiB (1 instance)
L3 cache:                             16 MiB (1 instance)
NUMA node(s):                         1
NUMA node0 CPU(s):                    0
Vulnerability Gather data sampling:   Not affected
Vulnerability Itlb multihit:          KVM: Mitigation: VMX unsupported
Vulnerability L1tf:                   Mitigation; PTE Inversion
Vulnerability Mds:                    Vulnerable: Clear CPU buffers attempted, no microcode; SMT Host state unknown
Vulnerability Meltdown:               Mitigation; PTI
Vulnerability Mmio stale data:        Unknown: No mitigations
Vulnerability Reg file data sampling: Not affected
Vulnerability Retbleed:               Not affected
Vulnerability Spec rstack overflow:   Not affected
Vulnerability Spec store bypass:      Vulnerable
Vulnerability Spectre v1:             Mitigation; usercopy/swapgs barriers and __user pointer sanitization
Vulnerability Spectre v2:             Mitigation; Retpolines; STIBP disabled; RSB filling; PBRSB-eIBRS Not affected; BHI Retpoline
Vulnerability Srbds:                  Not affected
Vulnerability Tsx async abort:        Not affected
```

### Memory Information
```
               total        used        free      shared  buff/cache   available
Mem:           1.4Gi       343Mi       234Mi       1.3Mi       1.0Gi       1.1Gi
Swap:             0B          0B          0B
```

### Disk Usage
```
Filesystem      Size  Used Avail Use% Mounted on
tmpfs           147M 1012K  146M   1% /run
/dev/sda1       8.7G  1.9G  6.8G  22% /
tmpfs           733M     0  733M   0% /dev/shm
tmpfs           5.0M     0  5.0M   0% /run/lock
/dev/sda16      881M   62M  758M   8% /boot
/dev/sda15      105M  6.2M   99M   6% /boot/efi
tmpfs           147M   12K  147M   1% /run/user/0
```

## Network Configuration
### Network Interfaces
```
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host noprefixroute 
       valid_lft forever preferred_lft forever
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc fq_codel state UP group default qlen 1000
    link/ether f8:c1:98:1c:db:f9 brd ff:ff:ff:ff:ff:ff
    altname enp0s18
    inet *************/24 brd ************** scope global eth0
       valid_lft forever preferred_lft forever
    inet6 fe80::fac1:98ff:fe1c:dbf9/64 scope link 
       valid_lft forever preferred_lft forever
```

### Routing Table
```
default via ************ dev eth0 proto static 
************/24 dev eth0 proto kernel scope link src ************* 
```

### DNS Configuration
```
# This is /run/systemd/resolve/stub-resolv.conf managed by man:systemd-resolved(8).
# Do not edit.
#
# This file might be symlinked as /etc/resolv.conf. If you're looking at
# /etc/resolv.conf and seeing this text, you have followed the symlink.
#
# This is a dynamic resolv.conf file for connecting local clients to the
# internal DNS stub resolver of systemd-resolved. This file lists all
# configured search domains.
#
# Run "resolvectl status" to see details about the uplink DNS servers
# currently in use.
#
# Third party programs should typically not access this file directly, but only
# through the symlink at /etc/resolv.conf. To manage man:resolv.conf(5) in a
# different way, replace this symlink by a static file or a different symlink.
#
# See man:systemd-resolved.service(8) for details about the supported modes of
# operation for /etc/resolv.conf.

nameserver **********
options edns0 trust-ad
search alexhost.com
```

## NGINX Configuration
### NGINX Version and Modules
```
```

### NGINX Service Status
```
● nginx.service - A high performance web server and a reverse proxy server
     Loaded: loaded (/usr/lib/systemd/system/nginx.service; enabled; preset: enabled)
     Active: active (running) since Thu 2025-07-17 00:35:37 UTC; 4 days ago
       Docs: man:nginx(8)
    Process: 120911 ExecStartPre=/usr/sbin/nginx -t -q -g daemon on; master_process on; (code=exited, status=0/SUCCESS)
    Process: 120924 ExecStart=/usr/sbin/nginx -g daemon on; master_process on; (code=exited, status=0/SUCCESS)
    Process: 148491 ExecReload=/usr/sbin/nginx -g daemon on; master_process on; -s reload (code=exited, status=0/SUCCESS)
   Main PID: 120930 (nginx)
      Tasks: 2 (limit: 1715)
     Memory: 14.2M (peak: 18.8M)
        CPU: 1min 41.691s
     CGroup: /system.slice/nginx.service
             ├─120930 "nginx: master process /usr/sbin/nginx -g daemon on; master_process on;"
             └─148493 "nginx: worker process"

Jul 17 00:35:37 backend2ndrevproxy systemd[1]: Starting nginx.service - A high performance web server and a reverse proxy server...
Jul 17 00:35:37 backend2ndrevproxy systemd[1]: Started nginx.service - A high performance web server and a reverse proxy server.
Jul 17 00:35:45 backend2ndrevproxy systemd[1]: Reloading nginx.service - A high performance web server and a reverse proxy server...
Jul 17 00:35:45 backend2ndrevproxy nginx[121171]: 2025/07/17 00:35:45 [notice] 121171#121171: signal process started
Jul 17 00:35:45 backend2ndrevproxy systemd[1]: Reloaded nginx.service - A high performance web server and a reverse proxy server.
Jul 19 20:05:04 backend2ndrevproxy systemd[1]: Reloading nginx.service - A high performance web server and a reverse proxy server...
Jul 19 20:05:05 backend2ndrevproxy nginx[148491]: 2025/07/19 20:05:05 [notice] 148491#148491: signal process started
Jul 19 20:05:05 backend2ndrevproxy systemd[1]: Reloaded nginx.service - A high performance web server and a reverse proxy server.
```

### NGINX Main Configuration
```nginx
user www-data;
worker_processes auto;
pid /run/nginx.pid;
error_log /var/log/nginx/error.log;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
	# multi_accept on;
}

http {

	##
	# Basic Settings
	##

	sendfile on;
	tcp_nopush on;
	types_hash_max_size 2048;
	# server_tokens off;

	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;

	##
	# Gzip Settings
	##

	gzip on;

	# gzip_vary on;
	# gzip_proxied any;
	# gzip_comp_level 6;
	# gzip_buffers 16 8k;
	# gzip_http_version 1.1;
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

	##
	# Virtual Host Configs
	##

	include /etc/nginx/conf.d/*.conf;
	include /etc/nginx/sites-enabled/*;
}


#mail {
#	# See sample authentication script at:
#	# http://wiki.nginx.org/ImapAuthenticateWithApachePhpScript
#
#	# auth_http localhost/auth.php;
#	# pop3_capabilities "TOP" "USER";
#	# imap_capabilities "IMAP4rev1" "UIDPLUS";
#
#	server {
#		listen     localhost:110;
#		protocol   pop3;
#		proxy      on;
#	}
#
#	server {
#		listen     localhost:143;
#		protocol   imap;
#		proxy      on;
#	}
#}
```

### NGINX Site Configurations
#### Configuration: streamdb.online
```nginx
# StreamDB.online Reverse Proxy Configuration
# Deploy to: /etc/nginx/sites-available/streamdb.online on *************
#
# INSTRUCTIONS:
# 1. Copy this entire configuration to your reverse proxy server (*************)
# 2. Save as: /etc/nginx/sites-available/streamdb.online
# 3. Enable with: ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/
# 4. Test with: nginx -t
# 5. Reload with: systemctl reload nginx

# WebSocket upgrade map
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# Main website - HTTP to HTTPS redirect
server {
    listen 80;
    server_name streamdb.online www.streamdb.online;
    
    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Redirect all HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Main website - HTTPS
server {
    listen 443 ssl http2;
    server_name streamdb.online www.streamdb.online;
    
    # SSL Configuration (Cloudflare handles certificates)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https: https://pertawee.net https://al5sm.com; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;

    #add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;
    
    # Hide server information
    server_tokens off;
    
    # Rate limiting
    limit_req zone=general burst=20 nodelay;
    
    # Proxy settings for backend
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # Proxy timeouts
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    
    # API routes with rate limiting - Direct to Node.js
    location /api/ {
        limit_req zone=api burst=10 nodelay;

        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Admin panel routes with extra security - Direct to Node.js
    location /admin {
        limit_req zone=admin burst=5 nodelay;

        # Additional security for admin
        add_header X-Admin-Access "Restricted" always;

        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Static files and main application - Direct to Node.js
    location / {
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://***********:3001;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Health check endpoint
    location /health {
        proxy_pass http://***********:3001/api/health;
        access_log off;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.env|\.git|node_modules|server|database) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Logging
    access_log /var/log/nginx/streamdb_access.log;
    error_log /var/log/nginx/streamdb_error.log;
}

# FastPanel subdomain - HTTP to HTTPS redirect
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    
    return 301 https://$server_name$request_uri;
}

# FastPanel subdomain - HTTPS
server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;
    
    # SSL Configuration (same as main site)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers for FastPanel (relaxed for functionality)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "same-origin" always;

    # FastPanel API endpoints - no rate limiting for better functionality
    location /api/ {
        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;

        # Disable buffering for API responses
        proxy_buffering off;
        proxy_request_buffering off;

        # Extended timeouts for API calls
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # FastPanel file manager and terminal endpoints
    location ~ ^/(local|www|api/files|api/terminal) {
        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;

        # Disable buffering for file operations
        proxy_buffering off;
        proxy_request_buffering off;

        # Extended timeouts for file operations
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # Increase body size for file uploads
        client_max_body_size 1G;
    }

    # Proxy to FastPanel on backend server (with light rate limiting)
    location / {
        # Light rate limiting only for general pages
        limit_req zone=admin burst=50 nodelay;

        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;

        # Disable proxy buffering for real-time responses
        proxy_buffering off;
        proxy_request_buffering off;

        # Longer timeouts for FastPanel
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Logging
    access_log /var/log/nginx/fastpanel_access.log;
    error_log /var/log/nginx/fastpanel_error.log;
}
```

### Additional NGINX Configurations
## SSL Certificates
### SSL Certificate Locations
```
/etc/ssl/private/cloudflare-origin.key
/etc/ssl/certs/Certum_Trusted_Network_CA_2.pem
/etc/ssl/certs/GlobalSign_Root_CA_-_R3.pem
/etc/ssl/certs/DigiCert_Global_Root_G2.pem
/etc/ssl/certs/QuoVadis_Root_CA_2_G3.pem
/etc/ssl/certs/Telia_Root_CA_v2.pem
/etc/ssl/certs/certSIGN_Root_CA_G2.pem
/etc/ssl/certs/Izenpe.com.pem
/etc/ssl/certs/AffirmTrust_Commercial.pem
/etc/ssl/certs/SwissSign_Silver_CA_-_G2.pem
/etc/ssl/certs/AffirmTrust_Networking.pem
/etc/ssl/certs/GlobalSign_Root_CA.pem
/etc/ssl/certs/SZAFIR_ROOT_CA2.pem
/etc/ssl/certs/Certainly_Root_R1.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority_-_EC1.pem
/etc/ssl/certs/Security_Communication_ECC_RootCA1.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority_-_G4.pem
/etc/ssl/certs/DigiCert_TLS_RSA4096_Root_G5.pem
/etc/ssl/certs/Trustwave_Global_ECC_P384_Certification_Authority.pem
/etc/ssl/certs/cloudflare-origin.pem
/etc/ssl/certs/USERTrust_ECC_Certification_Authority.pem
/etc/ssl/certs/BJCA_Global_Root_CA1.pem
/etc/ssl/certs/Go_Daddy_Class_2_CA.pem
/etc/ssl/certs/D-TRUST_EV_Root_CA_1_2020.pem
/etc/ssl/certs/OISTE_WISeKey_Global_Root_GB_CA.pem
/etc/ssl/certs/Sectigo_Public_Server_Authentication_Root_E46.pem
/etc/ssl/certs/Comodo_AAA_Services_root.pem
/etc/ssl/certs/GLOBALTRUST_2020.pem
/etc/ssl/certs/DigiCert_Assured_ID_Root_G3.pem
/etc/ssl/certs/SSL.com_TLS_ECC_Root_CA_2022.pem
/etc/ssl/certs/CommScope_Public_Trust_RSA_Root-01.pem
/etc/ssl/certs/GlobalSign_ECC_Root_CA_-_R5.pem
/etc/ssl/certs/GlobalSign_Root_E46.pem
/etc/ssl/certs/Starfield_Root_Certificate_Authority_-_G2.pem
/etc/ssl/certs/T-TeleSec_GlobalRoot_Class_3.pem
/etc/ssl/certs/NetLock_Arany_=Class_Gold=_Főtanúsítvány.pem
/etc/ssl/certs/Secure_Global_CA.pem
/etc/ssl/certs/IdenTrust_Public_Sector_Root_CA_1.pem
/etc/ssl/certs/ISRG_Root_X2.pem
/etc/ssl/certs/NAVER_Global_Root_Certification_Authority.pem
/etc/ssl/certs/Hellenic_Academic_and_Research_Institutions_RootCA_2015.pem
/etc/ssl/certs/SSL.com_TLS_RSA_Root_CA_2022.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority.pem
/etc/ssl/certs/Atos_TrustedRoot_Root_CA_RSA_TLS_2021.pem
/etc/ssl/certs/Sectigo_Public_Server_Authentication_Root_R46.pem
/etc/ssl/certs/UCA_Global_G2_Root.pem
/etc/ssl/certs/emSign_ECC_Root_CA_-_G3.pem
/etc/ssl/certs/DigiCert_Assured_ID_Root_CA.pem
/etc/ssl/certs/ANF_Secure_Server_Root_CA.pem
/etc/ssl/certs/Security_Communication_RootCA3.pem
/etc/ssl/certs/AC_RAIZ_FNMT-RCM.pem
/etc/ssl/certs/DigiCert_Trusted_Root_G4.pem
/etc/ssl/certs/certSIGN_ROOT_CA.pem
/etc/ssl/certs/CommScope_Public_Trust_ECC_Root-01.pem
/etc/ssl/certs/SSL.com_EV_Root_Certification_Authority_ECC.pem
/etc/ssl/certs/TunTrust_Root_CA.pem
/etc/ssl/certs/Buypass_Class_3_Root_CA.pem
/etc/ssl/certs/Hellenic_Academic_and_Research_Institutions_ECC_RootCA_2015.pem
/etc/ssl/certs/SwissSign_Gold_CA_-_G2.pem
/etc/ssl/certs/Starfield_Services_Root_Certificate_Authority_-_G2.pem
/etc/ssl/certs/Atos_TrustedRoot_2011.pem
/etc/ssl/certs/SSL.com_EV_Root_Certification_Authority_RSA_R2.pem
/etc/ssl/certs/Baltimore_CyberTrust_Root.pem
/etc/ssl/certs/Amazon_Root_CA_3.pem
/etc/ssl/certs/Trustwave_Global_ECC_P256_Certification_Authority.pem
/etc/ssl/certs/DigiCert_Assured_ID_Root_G2.pem
/etc/ssl/certs/AffirmTrust_Premium_ECC.pem
/etc/ssl/certs/AC_RAIZ_FNMT-RCM_SERVIDORES_SEGUROS.pem
/etc/ssl/certs/QuoVadis_Root_CA_1_G3.pem
/etc/ssl/certs/GTS_Root_R2.pem
/etc/ssl/certs/Amazon_Root_CA_4.pem
/etc/ssl/certs/Security_Communication_Root_CA.pem
/etc/ssl/certs/CA_Disig_Root_R2.pem
/etc/ssl/certs/GlobalSign_Root_CA_-_R6.pem
/etc/ssl/certs/D-TRUST_BR_Root_CA_1_2020.pem
/etc/ssl/certs/ACCVRAIZ1.pem
/etc/ssl/certs/Certainly_Root_E1.pem
/etc/ssl/certs/CommScope_Public_Trust_ECC_Root-02.pem
/etc/ssl/certs/COMODO_ECC_Certification_Authority.pem
/etc/ssl/certs/Security_Communication_RootCA2.pem
/etc/ssl/certs/Hongkong_Post_Root_CA_3.pem
/etc/ssl/certs/Microsoft_RSA_Root_Certificate_Authority_2017.pem
/etc/ssl/certs/GlobalSign_Root_R46.pem
/etc/ssl/certs/D-TRUST_Root_Class_3_CA_2_2009.pem
/etc/ssl/certs/DigiCert_High_Assurance_EV_Root_CA.pem
/etc/ssl/certs/TeliaSonera_Root_CA_v1.pem
/etc/ssl/certs/QuoVadis_Root_CA_3.pem
/etc/ssl/certs/HARICA_TLS_ECC_Root_CA_2021.pem
/etc/ssl/certs/ca-certificates.crt
/etc/ssl/certs/BJCA_Global_Root_CA2.pem
/etc/ssl/certs/DigiCert_TLS_ECC_P384_Root_G5.pem
/etc/ssl/certs/vTrus_ECC_Root_CA.pem
/etc/ssl/certs/HARICA_TLS_RSA_Root_CA_2021.pem
/etc/ssl/certs/SSL.com_Root_Certification_Authority_RSA.pem
/etc/ssl/certs/GlobalSign_ECC_Root_CA_-_R4.pem
/etc/ssl/certs/Certum_EC-384_CA.pem
/etc/ssl/certs/TWCA_Root_Certification_Authority.pem
/etc/ssl/certs/COMODO_Certification_Authority.pem
/etc/ssl/certs/QuoVadis_Root_CA_3_G3.pem
/etc/ssl/certs/emSign_Root_CA_-_G1.pem
/etc/ssl/certs/TUBITAK_Kamu_SM_SSL_Kok_Sertifikasi_-_Surum_1.pem
/etc/ssl/certs/Certum_Trusted_Network_CA.pem
/etc/ssl/certs/GTS_Root_R1.pem
/etc/ssl/certs/DigiCert_Global_Root_G3.pem
/etc/ssl/certs/Atos_TrustedRoot_Root_CA_ECC_TLS_2021.pem
/etc/ssl/certs/QuoVadis_Root_CA_2.pem
/etc/ssl/certs/USERTrust_RSA_Certification_Authority.pem
/etc/ssl/certs/ISRG_Root_X1.pem
/etc/ssl/certs/IdenTrust_Commercial_Root_CA_1.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority_-_G2.pem
/etc/ssl/certs/vTrus_Root_CA.pem
/etc/ssl/certs/Autoridad_de_Certificacion_Firmaprofesional_CIF_A62634068.pem
/etc/ssl/certs/T-TeleSec_GlobalRoot_Class_2.pem
/etc/ssl/certs/Starfield_Class_2_CA.pem
/etc/ssl/certs/Microsec_e-Szigno_Root_CA_2009.pem
/etc/ssl/certs/COMODO_RSA_Certification_Authority.pem
/etc/ssl/certs/Go_Daddy_Root_Certificate_Authority_-_G2.pem
/etc/ssl/certs/emSign_Root_CA_-_C1.pem
/etc/ssl/certs/GDCA_TrustAUTH_R5_ROOT.pem
/etc/ssl/certs/OISTE_WISeKey_Global_Root_GC_CA.pem
/etc/ssl/certs/GTS_Root_R3.pem
/etc/ssl/certs/Actalis_Authentication_Root_CA.pem
/etc/ssl/certs/XRamp_Global_CA_Root.pem
/etc/ssl/certs/ePKI_Root_Certification_Authority.pem
/etc/ssl/certs/SecureSign_RootCA11.pem
/etc/ssl/certs/TWCA_Global_Root_CA.pem
/etc/ssl/certs/TrustAsia_Global_Root_CA_G4.pem
/etc/ssl/certs/HiPKI_Root_CA_-_G1.pem
/etc/ssl/certs/AffirmTrust_Premium.pem
/etc/ssl/certs/CommScope_Public_Trust_RSA_Root-02.pem
/etc/ssl/certs/Amazon_Root_CA_1.pem
/etc/ssl/certs/SSL.com_Root_Certification_Authority_ECC.pem
/etc/ssl/certs/Certum_Trusted_Root_CA.pem
/etc/ssl/certs/Buypass_Class_2_Root_CA.pem
/etc/ssl/certs/emSign_ECC_Root_CA_-_C3.pem
/etc/ssl/certs/Certigna.pem
/etc/ssl/certs/UCA_Extended_Validation_Root.pem
/etc/ssl/certs/SecureTrust_CA.pem
/etc/ssl/certs/Certigna_Root_CA.pem
/etc/ssl/certs/GTS_Root_R4.pem
/etc/ssl/certs/Microsoft_ECC_Root_Certificate_Authority_2017.pem
/etc/ssl/certs/CFCA_EV_ROOT.pem
/etc/ssl/certs/DigiCert_Global_Root_CA.pem
/etc/ssl/certs/TrustAsia_Global_Root_CA_G3.pem
/etc/ssl/certs/Trustwave_Global_Certification_Authority.pem
/etc/ssl/certs/Entrust.net_Premium_2048_Secure_Server_CA.pem
/etc/ssl/certs/Amazon_Root_CA_2.pem
/etc/ssl/certs/e-Szigno_Root_CA_2017.pem
/etc/ssl/certs/D-TRUST_Root_Class_3_CA_2_EV_2009.pem
```

### Let's Encrypt Configuration
```
```

## Firewall Configuration
### UFW Status
```
Status: active
Logging: on (low)
Default: deny (incoming), allow (outgoing), disabled (routed)
New profiles: skip

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW IN    Anywhere                  
80/tcp                     ALLOW IN    Anywhere                  
443/tcp                    ALLOW IN    Anywhere                  
22/tcp (v6)                ALLOW IN    Anywhere (v6)             
80/tcp (v6)                ALLOW IN    Anywhere (v6)             
443/tcp (v6)               ALLOW IN    Anywhere (v6)             

```

### IPTables Rules
```
Chain INPUT (policy DROP 308K packets, 16M bytes)
 pkts bytes target     prot opt in     out     source               destination         
5138K 1977M ufw-before-logging-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
5138K 1977M ufw-before-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
1149K  274M ufw-after-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
 308K   16M ufw-after-logging-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
 308K   16M ufw-reject-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
 308K   16M ufw-track-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain FORWARD (policy DROP 0 packets, 0 bytes)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 ufw-before-logging-forward  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
    0     0 ufw-before-forward  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
    0     0 ufw-after-forward  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
    0     0 ufw-after-logging-forward  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
    0     0 ufw-reject-forward  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
    0     0 ufw-track-forward  0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain OUTPUT (policy ACCEPT 10571 packets, 626K bytes)
 pkts bytes target     prot opt in     out     source               destination         
4328K 1983M ufw-before-logging-output  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
4328K 1983M ufw-before-output  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
 342K   21M ufw-after-output  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
 342K   21M ufw-after-logging-output  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
 342K   21M ufw-reject-output  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
 342K   21M ufw-track-output  0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-after-forward (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-after-input (1 references)
 pkts bytes target     prot opt in     out     source               destination         
20861 1641K ufw-skip-to-policy-input  17   --  *      *       0.0.0.0/0            0.0.0.0/0            udp dpt:137
32014 7320K ufw-skip-to-policy-input  17   --  *      *       0.0.0.0/0            0.0.0.0/0            udp dpt:138
  508 27848 ufw-skip-to-policy-input  6    --  *      *       0.0.0.0/0            0.0.0.0/0            tcp dpt:139
 2723  137K ufw-skip-to-policy-input  6    --  *      *       0.0.0.0/0            0.0.0.0/0            tcp dpt:445
 712K  233M ufw-skip-to-policy-input  17   --  *      *       0.0.0.0/0            0.0.0.0/0            udp dpt:67
    0     0 ufw-skip-to-policy-input  17   --  *      *       0.0.0.0/0            0.0.0.0/0            udp dpt:68
73382   16M ufw-skip-to-policy-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0            ADDRTYPE match dst-type BROADCAST

Chain ufw-after-logging-forward (1 references)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 LOG        0    --  *      *       0.0.0.0/0            0.0.0.0/0            limit: avg 3/min burst 10 LOG flags 0 level 4 prefix "[UFW BLOCK] "

Chain ufw-after-logging-input (1 references)
 pkts bytes target     prot opt in     out     source               destination         
75574 3683K LOG        0    --  *      *       0.0.0.0/0            0.0.0.0/0            limit: avg 3/min burst 10 LOG flags 0 level 4 prefix "[UFW BLOCK] "

Chain ufw-after-logging-output (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-after-output (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-before-forward (1 references)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 ACCEPT     0    --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate RELATED,ESTABLISHED
    0     0 ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 3
    0     0 ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 11
    0     0 ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 12
    0     0 ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 8
    0     0 ufw-user-forward  0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-before-input (1 references)
 pkts bytes target     prot opt in     out     source               destination         
 3245  387K ACCEPT     0    --  lo     *       0.0.0.0/0            0.0.0.0/0           
3883K 1693M ACCEPT     0    --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate RELATED,ESTABLISHED
 7903 2809K ufw-logging-deny  0    --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate INVALID
 7903 2809K DROP       0    --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate INVALID
    0     0 ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 3
    0     0 ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 11
    0     0 ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 12
 5919  339K ACCEPT     1    --  *      *       0.0.0.0/0            0.0.0.0/0            icmptype 8
    0     0 ACCEPT     17   --  *      *       0.0.0.0/0            0.0.0.0/0            udp spt:67 dpt:68
1237K  280M ufw-not-local  0    --  *      *       0.0.0.0/0            0.0.0.0/0           
    0     0 ACCEPT     17   --  *      *       0.0.0.0/0            224.0.0.251          udp dpt:5353
    0     0 ACCEPT     17   --  *      *       0.0.0.0/0            239.255.255.250      udp dpt:1900
1237K  280M ufw-user-input  0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-before-logging-forward (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-before-logging-input (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-before-logging-output (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-before-output (1 references)
 pkts bytes target     prot opt in     out     source               destination         
 3245  387K ACCEPT     0    --  *      lo      0.0.0.0/0            0.0.0.0/0           
3982K 1962M ACCEPT     0    --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate RELATED,ESTABLISHED
 342K   21M ufw-user-output  0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-logging-allow (0 references)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 LOG        0    --  *      *       0.0.0.0/0            0.0.0.0/0            limit: avg 3/min burst 10 LOG flags 0 level 4 prefix "[UFW ALLOW] "

Chain ufw-logging-deny (2 references)
 pkts bytes target     prot opt in     out     source               destination         
 7542 2601K RETURN     0    --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate INVALID limit: avg 3/min burst 10
  257  151K LOG        0    --  *      *       0.0.0.0/0            0.0.0.0/0            limit: avg 3/min burst 10 LOG flags 0 level 4 prefix "[UFW BLOCK] "

Chain ufw-not-local (1 references)
 pkts bytes target     prot opt in     out     source               destination         
 400K   22M RETURN     0    --  *      *       0.0.0.0/0            0.0.0.0/0            ADDRTYPE match dst-type LOCAL
    0     0 RETURN     0    --  *      *       0.0.0.0/0            0.0.0.0/0            ADDRTYPE match dst-type MULTICAST
 838K  258M RETURN     0    --  *      *       0.0.0.0/0            0.0.0.0/0            ADDRTYPE match dst-type BROADCAST
    0     0 ufw-logging-deny  0    --  *      *       0.0.0.0/0            0.0.0.0/0            limit: avg 3/min burst 10
    0     0 DROP       0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-reject-forward (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-reject-input (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-reject-output (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-skip-to-policy-forward (0 references)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 DROP       0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-skip-to-policy-input (7 references)
 pkts bytes target     prot opt in     out     source               destination         
 841K  258M DROP       0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-skip-to-policy-output (0 references)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 ACCEPT     0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-track-forward (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-track-input (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-track-output (1 references)
 pkts bytes target     prot opt in     out     source               destination         
 330K   20M ACCEPT     6    --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate NEW
 1627  128K ACCEPT     17   --  *      *       0.0.0.0/0            0.0.0.0/0            ctstate NEW

Chain ufw-user-forward (1 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-user-input (1 references)
 pkts bytes target     prot opt in     out     source               destination         
53161 3142K ACCEPT     6    --  *      *       0.0.0.0/0            0.0.0.0/0            tcp dpt:22
 9296  541K ACCEPT     6    --  *      *       0.0.0.0/0            0.0.0.0/0            tcp dpt:80
25607 1619K ACCEPT     6    --  *      *       0.0.0.0/0            0.0.0.0/0            tcp dpt:443

Chain ufw-user-limit (0 references)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 LOG        0    --  *      *       0.0.0.0/0            0.0.0.0/0            limit: avg 3/min burst 5 LOG flags 0 level 4 prefix "[UFW LIMIT BLOCK] "
    0     0 REJECT     0    --  *      *       0.0.0.0/0            0.0.0.0/0            reject-with icmp-port-unreachable

Chain ufw-user-limit-accept (0 references)
 pkts bytes target     prot opt in     out     source               destination         
    0     0 ACCEPT     0    --  *      *       0.0.0.0/0            0.0.0.0/0           

Chain ufw-user-logging-forward (0 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-user-logging-input (0 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-user-logging-output (0 references)
 pkts bytes target     prot opt in     out     source               destination         

Chain ufw-user-output (1 references)
 pkts bytes target     prot opt in     out     source               destination         
```

## Running Services
### Active Services
```
  UNIT                                           LOAD   ACTIVE SUB     DESCRIPTION
  apparmor.service                               loaded active exited  Load AppArmor profiles
  apport.service                                 loaded active exited  automatic crash report generation
  blk-availability.service                       loaded active exited  Availability of block devices
  cloud-config.service                           loaded active exited  Cloud-init: Config Stage
  cloud-final.service                            loaded active exited  Cloud-init: Final Stage
  cloud-init-local.service                       loaded active exited  Cloud-init: Local Stage (pre-network)
  cloud-init.service                             loaded active exited  Cloud-init: Network Stage
  console-setup.service                          loaded active exited  Set console font and keymap
  cron.service                                   loaded active running Regular background program processing daemon
  dbus.service                                   loaded active running D-Bus System Message Bus
  fail2ban.service                               loaded active running Fail2Ban Service
  finalrd.service                                loaded active exited  Create final runtime dir for shutdown pivot root
  fwupd.service                                  loaded active running Firmware update daemon
  <EMAIL>                             loaded active running Getty on tty1
  keyboard-setup.service                         loaded active exited  Set the console keyboard layout
  kmod-static-nodes.service                      loaded active exited  Create List of Static Device Nodes
  lvm2-monitor.service                           loaded active exited  Monitoring of LVM2 mirrors, snapshots etc. using dmeventd or progress polling
  ModemManager.service                           loaded active running Modem Manager
  multipathd.service                             loaded active running Device-Mapper Multipath Device Controller
  nginx.service                                  loaded active running A high performance web server and a reverse proxy server
  plymouth-quit-wait.service                     loaded active exited  Hold until boot process finishes up
  plymouth-quit.service                          loaded active exited  Terminate Plymouth Boot Screen
  plymouth-read-write.service                    loaded active exited  Tell Plymouth To Write Out Runtime Data
  polkit.service                                 loaded active running Authorization Manager
  rsyslog.service                                loaded active running System Logging Service
  <EMAIL>                     loaded active running Serial Getty on ttyS0
  setvtrgb.service                               loaded active exited  Set console scheme
  snapd.apparmor.service                         loaded active exited  Load AppArmor profiles managed internally by snapd
  snapd.seeded.service                           loaded active exited  Wait until snapd is fully seeded
  ssh.service                                    loaded active running OpenBSD Secure Shell server
  sysstat.service                                loaded active exited  Resets System Activity Logs
  systemd-binfmt.service                         loaded active exited  Set Up Additional Binary Formats
  systemd-fsck@dev-disk-by\x2dlabel-BOOT.service loaded active exited  File System Check on /dev/disk/by-label/BOOT
  systemd-fsck@dev-disk-by\x2dlabel-UEFI.service loaded active exited  File System Check on /dev/disk/by-label/UEFI
  systemd-journal-flush.service                  loaded active exited  Flush Journal to Persistent Storage
  systemd-journald.service                       loaded active running Journal Service
  systemd-logind.service                         loaded active running User Login Management
  systemd-modules-load.service                   loaded active exited  Load Kernel Modules
  systemd-networkd-wait-online.service           loaded active exited  Wait for Network to be Configured
  systemd-networkd.service                       loaded active running Network Configuration
  systemd-random-seed.service                    loaded active exited  Load/Save OS Random Seed
  systemd-remount-fs.service                     loaded active exited  Remount Root and Kernel File Systems
  systemd-resolved.service                       loaded active running Network Name Resolution
  systemd-sysctl.service                         loaded active exited  Apply Kernel Variables
  systemd-timesyncd.service                      loaded active running Network Time Synchronization
  systemd-tmpfiles-setup-dev-early.service       loaded active exited  Create Static Device Nodes in /dev gracefully
  systemd-tmpfiles-setup-dev.service             loaded active exited  Create Static Device Nodes in /dev
  systemd-tmpfiles-setup.service                 loaded active exited  Create Volatile Files and Directories
  systemd-udev-trigger.service                   loaded active exited  Coldplug All udev Devices
  systemd-udevd.service                          loaded active running Rule-based Manager for Device Events and Files
  systemd-update-utmp.service                    loaded active exited  Record System Boot/Shutdown in UTMP
  systemd-user-sessions.service                  loaded active exited  Permit User Sessions
  udisks2.service                                loaded active running Disk Manager
  ufw.service                                    loaded active exited  Uncomplicated firewall
  unattended-upgrades.service                    loaded active running Unattended Upgrades Shutdown
  <EMAIL>                     loaded active exited  User Runtime Directory /run/user/0
  <EMAIL>                                 loaded active running User Manager for UID 0

Legend: LOAD   → Reflects whether the unit definition was properly loaded.
        ACTIVE → The high-level unit activation state, i.e. generalization of SUB.
        SUB    → The low-level unit activation state, values depend on unit type.

57 loaded units listed.
```

### Open Ports
```
```

## Log File Locations
### NGINX Logs
```
total 9620
drwxr-xr-x  2 <USER>     <GROUP>       4096 Jul 21 00:00 .
drwxr-xr-x 12 <USER>     <GROUP>    4096 Jul 20 00:00 ..
-rw-r-----  1 <USER> <GROUP>      24395 Jul 21 14:59 access.log
-rw-r-----  1 <USER> <GROUP>      32473 Jul 20 23:54 access.log.1
-rw-r-----  1 <USER> <GROUP>       6892 Jul 12 23:54 access.log.10.gz
-rw-r-----  1 <USER> <GROUP>       7371 Jul 11 23:37 access.log.11.gz
-rw-r-----  1 <USER> <GROUP>       7414 Jul 10 23:57 access.log.12.gz
-rw-r-----  1 <USER> <GROUP>       4628 Jul  9 23:28 access.log.13.gz
-rw-r-----  1 <USER> <GROUP>       6063 Jul  8 23:27 access.log.14.gz
-rw-r-----  1 <USER> <GROUP>       5736 Jul 19 23:40 access.log.2.gz
-rw-r-----  1 <USER> <GROUP>       7152 Jul 18 23:59 access.log.3.gz
-rw-r-----  1 <USER> <GROUP>       6039 Jul 17 23:57 access.log.4.gz
-rw-r-----  1 <USER> <GROUP>        206 Jul 17 00:19 access.log.5.gz
-rw-r-----  1 <USER> <GROUP>       5741 Jul 16 23:52 access.log.6.gz
-rw-r-----  1 <USER> <GROUP>       7004 Jul 15 23:54 access.log.7.gz
-rw-r-----  1 <USER> <GROUP>       6468 Jul 14 23:56 access.log.8.gz
-rw-r-----  1 <USER> <GROUP>       7354 Jul 13 23:47 access.log.9.gz
-rw-r-----  1 <USER> <GROUP>          0 Jul  5 00:00 error.log
-rw-r-----  1 <USER> <GROUP>        213 Jul  4 03:18 error.log.1
-rw-r-----  1 <USER> <GROUP>         94 Jul  3 18:33 error.log.2.gz
-rw-r-----  1 <USER> <GROUP>    3577968 Jul 21 15:07 fastpanel_access.log
-rw-r-----  1 <USER> <GROUP>    4471985 Jul 20 22:35 fastpanel_access.log.1
-rw-r-----  1 <USER> <GROUP>      45043 Jul 11 21:52 fastpanel_access.log.10.gz
-rw-r-----  1 <USER> <GROUP>      81812 Jul 11 00:00 fastpanel_access.log.11.gz
-rw-r-----  1 <USER> <GROUP>       3314 Jul 10 00:35 fastpanel_access.log.12.gz
-rw-r-----  1 <USER> <GROUP>      62123 Jul 10 00:00 fastpanel_access.log.13.gz
-rw-r-----  1 <USER> <GROUP>      85941 Jul  8 23:39 fastpanel_access.log.14.gz
-rw-r-----  1 <USER> <GROUP>      89380 Jul 19 20:51 fastpanel_access.log.2.gz
-rw-r-----  1 <USER> <GROUP>      62040 Jul 18 18:56 fastpanel_access.log.3.gz
-rw-r-----  1 <USER> <GROUP>      88927 Jul 17 19:44 fastpanel_access.log.4.gz
-rw-r-----  1 <USER> <GROUP>     114488 Jul 16 19:12 fastpanel_access.log.5.gz
-rw-r-----  1 <USER> <GROUP>      84917 Jul 15 18:51 fastpanel_access.log.6.gz
-rw-r-----  1 <USER> <GROUP>      75812 Jul 14 21:40 fastpanel_access.log.7.gz
-rw-r-----  1 <USER> <GROUP>      43784 Jul 13 15:35 fastpanel_access.log.8.gz
-rw-r-----  1 <USER> <GROUP>      19493 Jul 12 16:29 fastpanel_access.log.9.gz
-rw-r-----  1 <USER> <GROUP>          0 Jul 21 00:00 fastpanel_error.log
-rw-r-----  1 <USER> <GROUP>       1302 Jul 20 16:11 fastpanel_error.log.1
-rw-r--r--  1 <USER> <GROUP>       819 Jul  4 04:04 fastpanel_error.log.2.gz
-rw-r-----  1 <USER> <GROUP>      70152 Jul 21 15:06 streamdb_access.log
-rw-r-----  1 <USER> <GROUP>     347355 Jul 20 23:56 streamdb_access.log.1
-rw-r-----  1 <USER> <GROUP>      12269 Jul 12 23:53 streamdb_access.log.10.gz
-rw-r-----  1 <USER> <GROUP>       9209 Jul 11 23:24 streamdb_access.log.11.gz
-rw-r-----  1 <USER> <GROUP>      14127 Jul 10 23:57 streamdb_access.log.12.gz
-rw-r-----  1 <USER> <GROUP>        881 Jul 10 00:34 streamdb_access.log.13.gz
-rw-r-----  1 <USER> <GROUP>      16646 Jul  9 23:51 streamdb_access.log.14.gz
-rw-r-----  1 <USER> <GROUP>      21554 Jul 19 23:53 streamdb_access.log.2.gz
-rw-r-----  1 <USER> <GROUP>      40425 Jul 18 23:59 streamdb_access.log.3.gz
-rw-r-----  1 <USER> <GROUP>      15834 Jul 17 23:57 streamdb_access.log.4.gz
-rw-r-----  1 <USER> <GROUP>        420 Jul 17 00:34 streamdb_access.log.5.gz
-rw-r-----  1 <USER> <GROUP>      20142 Jul 16 23:29 streamdb_access.log.6.gz
-rw-r-----  1 <USER> <GROUP>      13405 Jul 15 23:48 streamdb_access.log.7.gz
-rw-r-----  1 <USER> <GROUP>      17437 Jul 14 23:54 streamdb_access.log.8.gz
-rw-r-----  1 <USER> <GROUP>       8934 Jul 13 23:31 streamdb_access.log.9.gz
-rw-r-----  1 <USER> <GROUP>       8941 Jul 21 14:54 streamdb_error.log
-rw-r-----  1 <USER> <GROUP>       5385 Jul 20 23:53 streamdb_error.log.1
-rw-r-----  1 <USER> <GROUP>       2226 Jul 12 20:17 streamdb_error.log.10.gz
-rw-r-----  1 <USER> <GROUP>       1056 Jul 11 22:27 streamdb_error.log.11.gz
-rw-r-----  1 <USER> <GROUP>       1478 Jul 10 23:22 streamdb_error.log.12.gz
-rw-r-----  1 <USER> <GROUP>        911 Jul  9 23:46 streamdb_error.log.13.gz
-rw-r-----  1 <USER> <GROUP>       1091 Jul  8 22:53 streamdb_error.log.14.gz
-rw-r-----  1 <USER> <GROUP>        727 Jul 19 19:21 streamdb_error.log.2.gz
-rw-r-----  1 <USER> <GROUP>       3777 Jul 18 17:29 streamdb_error.log.3.gz
-rw-r-----  1 <USER> <GROUP>       1034 Jul 17 22:17 streamdb_error.log.4.gz
-rw-r-----  1 <USER> <GROUP>        212 Jul 17 00:15 streamdb_error.log.5.gz
-rw-r-----  1 <USER> <GROUP>        773 Jul 16 17:13 streamdb_error.log.6.gz
-rw-r-----  1 <USER> <GROUP>        666 Jul 15 20:59 streamdb_error.log.7.gz
-rw-r-----  1 <USER> <GROUP>       2255 Jul 14 21:48 streamdb_error.log.8.gz
-rw-r-----  1 <USER> <GROUP>        877 Jul 13 21:09 streamdb_error.log.9.gz
```

### System Logs
```
drwxr-xr-x  12 <USER>      <GROUP>             4096 Jul 20 00:00 .
-rw-r-----   1 <USER>    <GROUP>             1949679 Jul 21 15:07 auth.log
-rw-r-----   1 <USER>    <GROUP>             4509976 Jul 20 00:00 auth.log.1
-rw-r-----   1 <USER>    <GROUP>              581406 Jul 17 00:35 auth.log.2.gz
-rw-r-----   1 <USER>    <GROUP>              625082 Jul 13 00:00 auth.log.3.gz
-rw-r-----   1 <USER>    <GROUP>              504648 Jul 10 00:35 auth.log.4.gz
-rw-r-----   1 <USER>    <GROUP>              198384 Jul  4 03:18 cloud-init.log.1
-rw-r-----   1 <USER>    <GROUP>               12238 Jul  2 19:15 cloud-init.log.2.gz
-rw-r-----   1 <USER>    <GROUP>             3305081 Jul 21 15:07 kern.log
-rw-r-----   1 <USER>    <GROUP>             6004556 Jul 20 00:00 kern.log.1
-rw-r-----   1 <USER>    <GROUP>              595656 Jul 17 00:35 kern.log.2.gz
-rw-r-----   1 <USER>    <GROUP>              416884 Jul 13 00:00 kern.log.3.gz
-rw-r-----   1 <USER>    <GROUP>              637281 Jul 10 00:35 kern.log.4.gz
drwxr-xr-x   2 <USER>      <GROUP>                4096 Jul 21 00:00 nginx
-rw-r-----   1 <USER>    <GROUP>             3513167 Jul 21 15:07 syslog
-rw-r-----   1 <USER>    <GROUP>             6355964 Jul 20 00:00 syslog.1
-rw-r-----   1 <USER>    <GROUP>              696789 Jul 17 00:35 syslog.2.gz
-rw-r-----   1 <USER>    <GROUP>              482683 Jul 13 00:00 syslog.3.gz
-rw-r-----   1 <USER>    <GROUP>              730696 Jul 10 00:35 syslog.4.gz
-rw-r-----   1 <USER>    <GROUP>             3304786 Jul 21 15:07 ufw.log
-rw-r-----   1 <USER>    <GROUP>             6004851 Jul 20 00:00 ufw.log.1
-rw-r-----   1 <USER>    <GROUP>              595108 Jul 17 00:35 ufw.log.2.gz
-rw-r-----   1 <USER>    <GROUP>              416884 Jul 13 00:00 ufw.log.3.gz
-rw-r-----   1 <USER>    <GROUP>              636599 Jul 10 00:35 ufw.log.4.gz
```

## Scheduled Tasks
### Root Crontab
```
```

## Installed Packages
### NGINX Related Packages
```
ii  nginx                           1.24.0-2ubuntu7.4                       amd64        small, powerful, scalable web/proxy server
ii  nginx-common                    1.24.0-2ubuntu7.4                       all          small, powerful, scalable web/proxy server - common files
ii  python3-certbot-nginx           2.9.0-1                                 all          Nginx plugin for Certbot
```

