# StreamDB Project Memory Tracker

## Overview
StreamDB is a comprehensive streaming content management system with a React TypeScript frontend and Node.js Express backend, designed for managing movies, TV series, and web series with robust authentication, SEO optimization, and secure video streaming capabilities.

## Architecture Overview

```mermaid
graph TB
    subgraph "Frontend (React + TypeScript)"
        A[Vite Dev Server] --> B[React Router]
        B --> C[Pages]
        B --> D[Components]
        B --> E[Contexts]
        E --> F[AuthContext]
        C --> G[API Service]
    end
    
    subgraph "Backend (Node.js + Express)"
        H[Express Server] --> I[Routes]
        I --> J[Middleware]
        I --> K[Services]
        K --> L[Database Layer]
    end
    
    subgraph "Database (MySQL)"
        L --> M[Content Table]
        L --> N[Categories Table]
        L --> O[Seasons/Episodes]
        L --> P[Admin Users]
    end
    
    subgraph "External APIs"
        Q[TMDB API]
        R[OMDB API]
    end
    
    G --> H
    K --> Q
    K --> R
    
    subgraph "Static Assets"
        S[Images/Videos]
        T[SEO Files]
        T --> U[Sitemap.xml]
        T --> V[Robots.txt]
    end
```

## Technology Stack

### Frontend Technologies
- **Framework**: React 18.3.1 with TypeScript 5.5.3
- **Build Tool**: Vite 5.4.1
- **Styling**: Tailwind CSS 3.4.11 with custom dark theme
- **UI Components**: Radix UI + shadcn/ui components
- **Routing**: React Router DOM 6.26.2
- **State Management**: 
  - React Context API (AuthContext)
  - TanStack React Query 5.56.2 for server state
- **Form Handling**: React Hook Form 7.53.0 with Zod validation
- **Icons**: Lucide React 0.462.0
- **Date Handling**: date-fns 3.6.0
- **Carousel**: Embla Carousel React 8.3.0

### Backend Technologies
- **Runtime**: Node.js (>=16.0.0)
- **Framework**: Express.js 4.18.2
- **Database**: MySQL 3.6.5 with mysql2 driver
- **Authentication**: 
  - bcryptjs 2.4.3 for password hashing
  - jsonwebtoken 9.0.2 for JWT tokens
  - express-session 1.17.3 for session management
- **Security**: 
  - helmet 7.1.0 for security headers
  - express-rate-limit 7.1.5 for rate limiting
  - cors 2.8.5 for CORS policy
- **Validation**: express-validator 7.0.1
- **File Upload**: multer 1.4.5-lts.1 + sharp 0.33.0 for image processing
- **Email**: nodemailer 6.10.1
- **Logging**: morgan 1.10.0

### Development Tools
- **Linting**: ESLint 9.9.0 with TypeScript ESLint 8.0.1
- **Package Manager**: npm
- **Process Manager**: PM2 (ecosystem.config.js)
- **Testing**: Jest 29.7.0 + Supertest 6.3.3

## Database Schema

### Core Tables

```mermaid
erDiagram
    categories ||--o{ content : "has"
    content ||--o{ seasons : "has"
    seasons ||--o{ episodes : "has"
    content ||--o{ content_genres : "belongs to"
    genres ||--o{ content_genres : "categorized as"
    content ||--o{ content_languages : "available in"
    languages ||--o{ content_languages : "supports"
    content ||--o{ content_quality : "has quality"
    quality_options ||--o{ content_quality : "defines"
    content ||--o{ content_audio : "has audio"
    audio_tracks ||--o{ content_audio : "provides"
    
    categories {
        int id PK
        varchar name
        enum type
        varchar slug
        text description
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    content {
        varchar id PK
        varchar title
        text description
        int year
        enum type
        int category_id FK
        varchar image
        varchar cover_image
        varchar tmdb_id
        decimal imdb_rating
        varchar runtime
        text secure_video_links
        boolean is_published
        boolean is_featured
        boolean add_to_carousel
        int total_seasons
        int total_episodes
        timestamp created_at
        timestamp updated_at
    }
    
    seasons {
        varchar id PK
        varchar content_id FK
        int season_number
        varchar title
        text description
        varchar poster_url
        timestamp created_at
        timestamp updated_at
    }
    
    episodes {
        varchar id PK
        varchar season_id FK
        varchar content_id FK
        int episode_number
        varchar title
        text description
        text secure_video_links
        varchar runtime
        date air_date
        varchar thumbnail_url
        timestamp created_at
        timestamp updated_at
    }
    
    admin_users {
        int id PK
        varchar username
        varchar password_hash
        varchar email
        enum role
        json permissions
        boolean is_active
        timestamp last_login
        timestamp created_at
    }
```

## Frontend Architecture

### Component Structure
```
src/
├── components/
│   ├── admin/           # Admin panel components
│   ├── auth/            # Authentication components
│   ├── ui/              # Reusable UI components (shadcn/ui)
│   ├── AllSeasonsModal.tsx
│   ├── CardGrid.tsx
│   ├── ErrorBoundary.tsx
│   ├── Footer.tsx
│   ├── Header.tsx
│   ├── HeroCarousel.tsx
│   ├── SafeImage.tsx
│   ├── SearchBar.tsx
│   ├── SecureVideoPlayer.tsx
│   └── SEOHead.tsx
├── contexts/
│   └── AuthContext.tsx  # Authentication state management
├── hooks/               # Custom React hooks
├── pages/               # Route components
├── services/            # API communication
├── types/               # TypeScript definitions
└── utils/               # Utility functions
```

### Key Components

#### Authentication System
- **AuthContext**: Centralized authentication state using useReducer
- **Session Storage**: Secure session management with sessionStorage
- **ProtectedRoute**: Route guard for admin areas
- **Login/ResetPassword**: Authentication UI components

#### Content Management
- **HeroCarousel**: Featured content carousel with image optimization
- **CardGrid**: Responsive content grid layout
- **ContentPage**: Individual content detail pages
- **SecureVideoPlayer**: Protected video streaming component
- **SafeImage**: Optimized image loading with fallbacks

#### Admin Features
- **AdminPanel**: Content management dashboard
- **AllSeasonsModal**: Season/episode management
- **ContentPreview**: Content preview functionality

### State Management Strategy
- **AuthContext**: Global authentication state
- **React Query**: Server state management and caching
- **Local State**: Component-specific state with useState/useReducer
- **URL State**: Route parameters and search params for navigation

### Routing Architecture
```mermaid
graph LR
    A[/] --> B[Index - Homepage]
    A --> C[/movies] --> D[AllMovies]
    A --> E[/series] --> F[AllSeries]
    A --> G[/category/:slug] --> H[CategoryPage]
    A --> I[/content/:id] --> J[ContentPage]
    A --> K[/admin] --> L[AdminPanel]
    A --> M[/login] --> N[Login]
    
    L --> O[/admin/player-test]
    L --> P[/admin/tmdb-test]
    L --> Q[/admin/content-preview]
```

## Backend Architecture

### Server Structure
```
server/
├── config/
│   └── database.js      # MySQL connection pool
├── middleware/          # Express middleware
├── routes/              # API endpoints
├── services/            # Business logic layer
├── tests/               # Test files
└── index.js            # Main server file
```

### API Endpoints

#### Authentication Routes (`/api/auth`)
- `POST /login` - User authentication
- `POST /logout` - Session termination
- `GET /status` - Check authentication status
- `POST /refresh` - Refresh session token
- `POST /reset-password` - Password reset functionality

#### Content Routes (`/api/content`)
- `GET /` - Fetch content with filtering and pagination
  - Supports filters: type, category, genre, language, year, featured, published
  - Full-text search capability
  - Sorting and pagination support
- `GET /:id` - Get specific content details with relationships
  - Includes genres, languages, quality, audio tracks, seasons
  - Returns complete content object with metadata
- `POST /` - Create new content (admin)
  - Validates required fields and relationships
  - Supports bulk data import via CSV
- `PUT /:id` - Update content (admin)
  - Partial updates supported
  - Maintains relationship integrity
- `DELETE /:id` - Delete content (admin)
  - Cascades to related seasons/episodes
  - Maintains referential integrity
- `GET /:id/seasons` - Get seasons for series
  - Returns season list with episode counts
- `GET /:id/episodes` - Get episodes for season
  - Ordered by episode number

#### Episodes Management (`/api/episodes`)
- `GET /content/:contentId` - Get all seasons/episodes for content
- `POST /seasons` - Create new season
  - Auto-generates season ID
  - Updates content total_seasons count
- `POST /episodes` - Create new episode
  - Auto-generates episode ID
  - Updates season and content episode counts
- `PUT /seasons/:id` - Update season information
- `PUT /episodes/:id` - Update episode details
- `DELETE /seasons/:id` - Remove season and all episodes
- `DELETE /episodes/:id` - Remove individual episode

#### Sections Management (`/api/sections`)
- `GET /` - List all content sections
  - Returns active sections with display configuration
- `GET /:slug/content` - Get content for specific section
  - Supports pagination and filtering
  - Returns content with quality labels
- `POST /` - Create new section (admin)
- `PUT /:id` - Update section configuration
- `DELETE /:id` - Remove section

#### Admin Operations (`/api/admin`)
- `GET /stats` - Dashboard statistics
  - Content counts by type and status
  - Recent activity summary
  - Database health metrics
- `GET /security-logs` - Security audit trail
  - Login attempts and access logs
  - Admin activity tracking
- `POST /bulk-upload` - CSV data import
  - Validates CSV format and data integrity
  - Supports content, seasons, episodes import
- `GET /database-status` - Database health check
  - Connection status and performance metrics
  - Table statistics and index health

### Security Implementation

#### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Password Hashing**: bcrypt with salt rounds
- **Session Management**: Express sessions with MySQL store
- **Role-Based Access**: Admin/moderator permission system

#### Security Headers & Middleware
```javascript
// CSP Policy with Monetag ads support
"default-src 'self'; " +
"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; " +
"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
"img-src 'self' data: https: blob:; " +
"connect-src 'self' https: wss:;"
```

#### Rate Limiting
- General API: 1000 requests/15min
- Auth endpoints: 500 requests/15min
- IP-based tracking with Redis fallback

### Database Layer

#### Connection Management
- **Connection Pool**: mysql2 connection pooling
- **Socket vs TCP**: Production uses socket, development uses TCP
- **SSL Support**: Configurable SSL for remote connections
- **Error Handling**: Comprehensive error classification system

#### Query Optimization
- **Prepared Statements**: All queries use prepared statements
- **Indexing Strategy**: Optimized indexes for search and filtering
- **Transaction Support**: Multi-query transactions for data consistency
- **Full-text Search**: MySQL FULLTEXT indexes for content search

## Service Integrations

### External API Services

#### TMDB Integration (`tmdbService.ts`)
- **Movie/TV Data**: Metadata fetching
- **Image URLs**: Poster and backdrop images
- **Search Functionality**: Content discovery
- **Rate Limiting**: API quota management

#### OMDB Integration (`omdbService.ts`)
- **IMDB Ratings**: Additional metadata
- **Plot Summaries**: Enhanced descriptions
- **Awards Information**: Award details

### Email Service
- **Nodemailer**: SMTP email sending
- **Password Reset**: Secure reset tokens
- **Admin Notifications**: System alerts

### File Storage
- **Local Storage**: Uploaded files management
- **Sharp Processing**: Image optimization and resizing
- **Secure Paths**: Protected file access

## SEO & Performance

### SEO Features
- **Dynamic Sitemap**: Auto-generated XML sitemap
- **Meta Tags**: Dynamic meta tags per page
- **Robots.txt**: Search engine directives
- **Schema Markup**: Structured data for rich snippets
- **URL Optimization**: SEO-friendly URLs with slugs

### Performance Optimizations
- **Image Optimization**: Sharp for image processing
- **Lazy Loading**: Components and images
- **Code Splitting**: Route-based code splitting with Vite
- **Compression**: Gzip compression on server
- **Caching**: Static asset caching with proper headers

## Development Workflow

### Build Process
```bash
# Development
npm run dev              # Start Vite dev server
cd server && npm run dev # Start backend with nodemon

# Production Build
npm run build            # Build frontend + generate sitemap
npm run deploy           # Build + SEO optimization

# SEO Tasks
npm run generate-sitemap # Generate XML sitemap
npm run seo-optimize     # Run SEO optimizations
npm run verify-seo       # Verify SEO implementation
```

### Testing Strategy
- **Backend Tests**: Jest + Supertest for API testing
- **Frontend Testing**: Component testing capabilities
- **Database Testing**: Isolated test database
- **Integration Tests**: End-to-end workflow testing

### Deployment Configuration
- **PM2**: Process management with ecosystem.config.js
- **Nginx**: Reverse proxy configuration
- **SSL**: HTTPS termination at proxy level
- **Environment**: Separate dev/staging/production configs

## Configuration Management

### Environment Variables
```bash
# Database
DB_HOST=localhost
DB_USER=streamdb_user
DB_PASSWORD=secure_password
DB_NAME=streamdb_database
DB_SSL=false

# Server
PORT=3001
NODE_ENV=production
SESSION_SECRET=secure_session_secret

# External APIs
TMDB_API_KEY=your_tmdb_key
OMDB_API_KEY=your_omdb_key

# Email
SMTP_HOST=smtp.example.com
SMTP_USER=your_email
SMTP_PASS=your_password
```

### Feature Flags
- **IS_PUBLISHED**: Content visibility control
- **IS_FEATURED**: Homepage featuring
- **ADD_TO_CAROUSEL**: Carousel inclusion
- **MAINTENANCE_MODE**: System maintenance state

## Error Handling & Monitoring

### Error Classification System
- **Database Errors**: Connection, query, constraint violations
- **Authentication Errors**: Invalid credentials, expired sessions
- **Validation Errors**: Input validation failures
- **External API Errors**: TMDB/OMDB service failures

### Logging Strategy
- **Morgan**: HTTP request logging
- **Console Logging**: Application events
- **Error Tracking**: Comprehensive error context
- **Performance Monitoring**: Query timing and optimization

## Security Considerations

### Data Protection
- **Input Validation**: All user inputs validated
- **SQL Injection Prevention**: Prepared statements only
- **XSS Protection**: Content sanitization
- **CSRF Protection**: Token-based CSRF prevention

### Video Security
- **Secure Links**: Encrypted video URL storage
- **Access Control**: Time-limited access tokens
- **Embed Protection**: Secure iframe embedding
- **Download Prevention**: Stream-only video delivery

## Automated Systems

### SEO Automation
- **Sitemap Generation**: Auto-updated XML sitemaps
- **Search Engine Ping**: Automatic search engine notifications
- **Meta Tag Generation**: Dynamic SEO meta tags
- **Performance Monitoring**: SEO health checks

### Maintenance Scripts
- **Database Cleanup**: Orphaned data removal
- **Cache Management**: Static asset cache control
- **Security Audits**: Automated security checks
- **Backup Systems**: Database backup automation

## Infrastructure & Deployment Architecture

### Server Infrastructure

```mermaid
graph TB
    subgraph "Internet"
        A[Client Browsers]
        B[Search Engines]
        C[CDN/Cloudflare]
    end
    
    subgraph "Reverse Proxy Server (*************)"
        D[Nginx Reverse Proxy]
        E[SSL Termination]
        F[Rate Limiting]
        G[Security Headers]
        H[Static Asset Caching]
        I[FastPanel Subdomain]
    end
    
    subgraph "Backend Server (***********)"
        J[Nginx Web Server]
        K[Node.js/Express API]
        L[PM2 Process Manager]
        M[MySQL Database]
        N[File Storage]
        O[FastPanel Control Panel]
    end
    
    subgraph "External Services"
        P[TMDB API]
        Q[OMDB API]
        R[SMTP Email Service]
    end
    
    A --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> J
    J --> K
    K --> L
    L --> M
    K --> N
    K --> P
    K --> Q
    K --> R
    
    I --> O
```

### Multi-Server Architecture

#### Reverse Proxy Server (*************)
**Role**: SSL termination, load balancing, security, static asset caching

**Hardware Specifications**:
- **Hostname**: backend2ndrevproxy
- **OS**: Ubuntu 24.04.2 LTS (Kernel 6.8.0-31-generic)
- **CPU**: 1x QEMU Virtual CPU @ 2.0GHz (x86_64)
- **Memory**: 1.4GB RAM (343MB used, 1.1GB available)
- **Storage**: 8.7GB total (1.9GB used, 6.8GB available)
- **Network**: *************/24 via eth0

**Services**:
- **Nginx**: Reverse proxy with SSL termination
- **Cloudflare Integration**: DNS and CDN services
- **Security Layer**: Rate limiting, DDoS protection, security headers
- **FastPanel Subdomain**: Admin panel access

**Configuration**:
```nginx
# SSL Configuration with Cloudflare
ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;

# Rate Limiting Zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# Security Headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
add_header X-Frame-Options "SAMEORIGIN";
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'";
```

#### Backend Server (***********)
**Role**: Application logic, database, file storage, content processing

**Hardware Specifications**:
- **Hostname**: backend1maindb
- **OS**: Ubuntu 24.04.2 LTS (Kernel 6.8.0-63-generic)
- **CPU**: 2x QEMU Virtual CPU @ 2.0GHz (x86_64)
- **Memory**: 4GB RAM (1.0GB used, 2.8GB available)
- **Storage**: 38GB total (6.0GB used, 32GB available)
- **Network**: ***********/24 via eth0

**Services**:
- **Node.js/Express**: API server on port 3001
- **MySQL**: Database server with optimized configuration
- **PM2**: Process management and monitoring
- **Nginx**: Local web server for static files
- **Apache**: Secondary server for FastPanel integration
- **FastPanel2**: Server management interface on port 8888

**Application Path**: `/var/www/streamdb_onl_usr/data/www/streamdb.online/`

**PM2 Configuration**:
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'streamdb-online',
      script: './server/index.js',
      instances: 1,
      exec_mode: 'fork',
      max_memory_restart: '1G',
      restart_delay: 5000,
      autorestart: true
    },
    {
      name: 'mysql-health',
      script: './server/services/mysql-health.js',
      max_memory_restart: '256M',
      restart_delay: 10000
    }
  ]
};
```

### Database Infrastructure

#### MySQL Configuration
**Database Name**: `stream_db`
**Server**: *********** (Backend Production - backend1maindb)
**MySQL Version**: 8.0.42-0ubuntu0.24.04.1 for Linux on x86_64
**Total Tables**: 17

**Production Database Statistics**:
| Table | Rows | Size (MB) | Status |
|-------|------|-----------|--------|
| ad_blocker_tracking | 87 | 0.11 | ✅ Active |
| admin_security_logs | 210 | 0.13 | ✅ Active |
| admin_users | 1 | 0.09 | ✅ Active |
| auth_tokens | 0 | 0.11 | ⚠️ Unused |
| categories | 18 | 0.09 | ✅ Active |
| content | 9 | 0.20 | ✅ Active |
| content_section_mappings | 60 | 0.06 | ✅ Active |
| content_sections | 6 | 0.11 | ✅ Active |
| episodes | 8 | 0.08 | ✅ Active |
| login_attempts | 0 | 0.09 | ⚠️ Unused |
| password_reset_tokens | 0 | 0.09 | ⚠️ Unused |
| seasons | 4 | 0.06 | ✅ Active |
| section_categories | 41 | 0.06 | ✅ Active |
| section_content_types | 0 | 0.06 | ⚠️ Unused |
| security_logs | 1158 | 0.63 | ✅ Heavily Used |
| sessions | 0 | 0.02 | ⚠️ Express Sessions |
| user_sessions | 0 | 0.06 | ⚠️ Unused |

**Connection Strategy**:
- **Production**: Socket connection (`/var/run/mysqld/mysqld.sock`)
- **Development**: TCP connection (`localhost:3306`)
- **SSL**: Configurable for remote connections
- **Connection Pooling**: mysql2 with 10 connection limit

**Enhanced Schema Features**:
- **Multi-Section Support**: `content_section_mappings` table for many-to-many relationships
- **Dual Section System**: Legacy `content.section_id` + new `content_section_mappings`
- **Enhanced Content Fields**: `carousel_position`, `crop_settings`, `quality_label`
- **JSON Metadata**: Languages, genres, quality, audio_tracks stored as JSON
- **Full-text Search**: FULLTEXT indexes on title, description, tags

**Active Content Sections**:
| ID | Name | Slug | Description |
|----|------|------|-------------|
| 1 | Movies | movies | Latest and popular movies collection |
| 2 | Web Series | web-series | Trending web series and TV shows |
| 3 | Requested | requested | User requested content |
| 4 | New Releases | new-releases | Recently added content |
| 6 | Drama | drama | Drama category content |

**Security Features**:
- Local-only access (no external exposure)
- Prepared statements only (SQL injection prevention)
- Role-based access control
- Automated backup system
- Comprehensive audit logging

**Performance Optimizations**:
- Optimized indexes for search and filtering
- Connection pooling with proper timeout handling
- Query optimization with diagnostic error classification
- Full-text search indexes for content discovery

#### Database Schema Architecture
```sql
-- Core content structure with relationships
CREATE TABLE content (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    type ENUM('movie', 'series', 'requested'),
    secure_video_links TEXT, -- Encrypted video URLs
    is_published BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    add_to_carousel BOOLEAN DEFAULT FALSE,
    FULLTEXT KEY ft_search (title, description, tags)
);

-- Normalized relationship tables
CREATE TABLE content_genres (
    content_id VARCHAR(50),
    genre_id INT,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE
);
```

### Deployment Infrastructure

#### Automated Deployment Pipeline
**Deployment Script Features**:
- **Git Integration**: Automated pulls from GitHub repository
- **Backup System**: Automatic backup before deployment
- **Rollback Capability**: Quick rollback to previous version
- **Dependency Management**: Automated npm install for both frontend and backend
- **Build Process**: Vite build with SEO sitemap generation
- **Service Management**: PM2 restart with zero-downtime

**Deployment Process Flow**:
```bash
#!/bin/bash
# Deployment phases
1. Prerequisites Check (Node.js, PM2, Git)
2. Backup Creation (with rotation - keep last 5)
3. Git Pull & Reset (to latest commit)
4. Dependency Installation (npm ci for both frontend/backend)
5. Frontend Build (Vite + sitemap generation)
6. Security Audit (dependency vulnerabilities)
7. Database Migration (if schema changes)
8. PM2 Restart (graceful restart)
9. Health Check (API endpoint verification)
10. Nginx Reload (configuration updates)
```

#### Environment Configuration
**Production Environment Variables**:
```bash
# Server Configuration
NODE_ENV=production
PORT=3001
SESSION_SECRET=secure_session_secret

# Database Configuration
DB_HOST=localhost
DB_USER=streamdb_user
DB_PASSWORD=secure_password
DB_NAME=streamdb_database
DB_SOCKET=/var/lib/mysql/mysql.sock

# External API Keys
TMDB_API_KEY=your_tmdb_api_key
OMDB_API_KEY=your_omdb_api_key

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_USER=<EMAIL>
SMTP_PASS=secure_email_password

# Security Settings
FRONTEND_URL=https://streamdb.online
ALLOWED_ORIGINS=https://streamdb.online,https://www.streamdb.online
```

### Automated Maintenance System

#### Maintenance Architecture
**Two-Server Coordination**:
- **Backend Maintenance**: Every Thursday at 00:00 (midnight)
- **Reverse Proxy Maintenance**: Every Thursday at 00:30 (30-minute offset)
- **Coordinated Updates**: Prevents service disruption

**Maintenance Features**:
```bash
# Backend Server Maintenance (backend-maintenance.sh)
- System package updates (security patches)
- Node.js/npm updates (with compatibility checks)
- PM2 health checks and restart if needed
- MySQL optimization and cleanup
- Log rotation and cleanup
- Backup verification
- SSL certificate renewal checks

# Reverse Proxy Maintenance (reverse-proxy-maintenance.sh)
- Nginx configuration validation
- SSL certificate monitoring
- Rate limiting rule updates
- Security header updates
- Log analysis and rotation
- Firewall rule optimization
```

#### Cron Job Configuration
```bash
# Backend Server (***********)
0 0 * * 4 /usr/local/bin/backend-maintenance.sh

# Reverse Proxy Server (*************)
30 0 * * 4 /usr/local/bin/reverse-proxy-maintenance.sh
```

### Security Infrastructure

#### Multi-Layer Security
**Network Security**:
- **Cloudflare**: DDoS protection, WAF, bot management
- **Nginx Rate Limiting**: API and admin endpoint protection
- **Firewall Rules**: Port restriction and IP whitelisting
- **SSL/TLS**: End-to-end encryption with certificate management

**Application Security**:
```javascript
// Security Headers Implementation
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://pertawee.net"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https:", "wss:"]
    }
  }
}));

// Rate Limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // requests per window
  message: 'Too many requests'
});
```

**Database Security**:
- **Local Access Only**: No external database exposure
- **Prepared Statements**: SQL injection prevention
- **Connection Encryption**: SSL for remote connections
- **Access Control**: Role-based database permissions

**Video Security**:
- **Encrypted URLs**: Secure video link storage
- **Time-Limited Access**: Expiring access tokens
- **Embed Protection**: Secure iframe implementation
- **Download Prevention**: Stream-only delivery

### Monitoring & Logging

#### Logging Architecture
**Application Logs**:
```bash
# PM2 Logs
/var/log/streamdb/app-error.log    # Application errors
/var/log/streamdb/app-out.log      # Application output
/var/log/streamdb/app-combined.log # Combined logs

# Nginx Logs
/var/log/nginx/streamdb_access.log # HTTP access logs
/var/log/nginx/streamdb_error.log  # Nginx error logs

# Maintenance Logs
/var/log/streamdb-maintenance/     # Automated maintenance logs
```

**Health Monitoring**:
- **MySQL Health Service**: Continuous database monitoring
- **API Health Endpoints**: `/health` endpoint for monitoring
- **PM2 Monitoring**: Process health and restart capability
- **Nginx Status**: Server status and performance metrics

#### Error Handling & Classification
**Database Error Classification**:
```javascript
class DatabaseErrorClassifier {
  classifyError(error) {
    return {
      category: this.determineCategory(error),
      severity: this.assessSeverity(error),
      solutions: this.suggestSolutions(error),
      recovery: this.getRecoverySteps(error)
    };
  }
}
```

### Disaster Recovery & Migration Procedures

#### IP Change Management
The infrastructure supports comprehensive IP change scenarios:

**Scenario A: Reverse Proxy IP Change (************* → NEW_IP)**
1. **Cloudflare DNS Update**: Update all A records (streamdb.online, www, fastpanel)
2. **SSL Certificate Migration**: Transfer Cloudflare origin certificates
3. **Nginx Configuration**: Deploy reverse proxy config on new server
4. **Backend Update**: No changes needed if backend IP remains same

**Scenario B: Backend IP Change (*********** → NEW_IP)**
1. **Database Migration**: Export/import MySQL database
2. **Application Migration**: Transfer complete application directory
3. **Environment Update**: Update .env files with new configurations
4. **Reverse Proxy Update**: Update upstream backend IP in nginx config
5. **PM2 Restart**: Restart application with new configuration

**Scenario C: Both IPs Change**
- Combines procedures from both scenarios above
- Requires coordinated migration of both tiers
- Includes comprehensive testing and rollback procedures

#### Domain Migration Support
Complete domain migration infrastructure with:

**Multi-Domain Setup**:
- Primary domain configuration
- 301 redirect chains for SEO preservation
- SSL certificate management for new domains
- Environment variable updates across all services

**Migration Steps**:
1. **DNS Configuration**: Setup new domain in Cloudflare
2. **Backend Environment**: Update FRONTEND_URL, CORS_ORIGIN, API endpoints
3. **Frontend Build**: Rebuild with new domain configuration
4. **Reverse Proxy**: Deploy nginx config for new domain
5. **Testing & Validation**: Comprehensive verification procedures

#### Reverse Proxy Verification System
Automated verification script that checks:
- DNS resolution to Cloudflare
- SSL certificate validity
- Proxy functionality and backend connectivity
- Security headers implementation
- Response consistency and performance
- Backend server protection (ensures backend is not directly accessible)

**Verification Script Features**:
```bash
# Key verification checks
- DNS resolution and Cloudflare detection
- SSL/TLS configuration validation
- Rate limiting functionality
- Security header verification
- Backend server accessibility tests
- Performance and response time monitoring
```

#### Backup & Recovery Procedures

**Database Backup Strategy**:
- **Automated Daily Backups**: MySQL dumps with 5-backup retention policy
- **Point-in-Time Recovery**: Binary log backup for granular recovery
- **Pre-deployment Backups**: Automatic backup before each deployment
- **Backup Location**: `/var/backups/streamdb-online/`
- **Backup Naming**: `backup-YYYYMMDD-HHMMSS` format

**Application Backup Strategy**:
- **Pre-Deployment Snapshots**: Complete application directory backup
- **Configuration Backups**: Nginx and PM2 configuration snapshots
- **File Storage Backups**: Uploaded content and media files
- **Environment Backups**: .env files with timestamp

**Recovery Procedures**:
```bash
# Database Recovery
mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < backup_file.sql

# Application Recovery
./deploy.sh rollback

# Configuration Recovery
cp /var/backups/nginx/nginx.conf.backup /etc/nginx/nginx.conf
systemctl reload nginx

# PM2 Application Recovery
pm2 delete streamdb-online
pm2 start ecosystem.config.js
```

**Backup Verification**:
- Automated backup integrity checks
- Recovery testing on staging environment
- Backup size and consistency monitoring

### Server Management Infrastructure

#### FastPanel2 Control Panel
**Access**: `fastpanel.streamdb.online:8888` (HTTPS)
**Purpose**: Comprehensive server administration and management

**Services Managed**:
- Apache 2.4 configuration and virtual hosts
- Nginx configuration and site management
- MySQL database administration and user management
- SSL certificate management and renewal
- User account and permission management
- File manager and backup operations

**Configuration Directories**:
```bash
# FastPanel2 Configuration Paths
/etc/nginx/fastpanel2-sites/streamdb_root/     # Nginx site configs
/etc/apache2/fastpanel2-sites/streamdb_root/  # Apache site configs
/etc/mysql/my.cnf.fastpanel/99-fastpanel.cnf  # MySQL configuration
```

#### Dual Web Server Architecture
**Traffic Flow**:
```
Internet Traffic (Port 80/443)
         ↓
    Nginx (Frontend)
    - SSL termination
    - Static file serving
    - Reverse proxy to Node.js
    - Load balancing
         ↓
    Apache (Backend)
    - FastPanel integration
    - PHP processing (if needed)
    - Application routing
         ↓
    Node.js Application (Port 3001)
    - API endpoints
    - Dynamic content
    - Database connections
```

**Web Server Roles**:
- **Nginx**: Primary web server handling public traffic on ports 80/443
  - SSL termination and static content delivery
  - Proxies API requests to Node.js application
  - Handles caching and compression
- **Apache**: Secondary server for FastPanel2 integration
  - Manages FastPanel2 web interface
  - Handles server administration tasks
  - Integrated with FastPanel2 configuration system

### Performance Optimization

#### Caching Strategy
**Static Asset Caching**:
```nginx
# Long-term caching for static assets
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# No cache for dynamic content
location ~* \.html$ {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

**Database Optimization**:
- **Connection Pooling**: Efficient connection management
- **Query Optimization**: Indexed searches and prepared statements
- **Result Caching**: TanStack Query for client-side caching

**Image Optimization**:
- **Sharp Processing**: Server-side image optimization
- **Lazy Loading**: Client-side performance optimization
- **WebP Support**: Modern image format delivery

### Scalability Considerations

#### Horizontal Scaling Preparation
**Load Balancing Ready**:
- **Stateless Architecture**: Session storage externalization
- **Database Connection Pooling**: Multi-instance support
- **Shared File Storage**: Centralized media storage capability

**Microservices Transition Path**:
- **Service Separation**: API routes modularization
- **Database Sharding**: Content/user data separation
- **Cache Layer**: Redis integration preparation

#### Performance Metrics
**Key Performance Indicators**:
- **Response Time**: API endpoint response times
- **Database Performance**: Query execution times
- **Memory Usage**: Node.js heap and PM2 monitoring
- **Network Throughput**: Nginx connection handling

---

*This memory tracker is automatically updated when project changes are detected. Last updated: $(date)*