#!/bin/bash
# CRITICAL DIAGNOSIS: Check if content exists and is published

echo "🚨 CRITICAL DIAGNOSIS: Content Existence & Publication Status"
echo "==========================================================="
echo "📅 Test Date: $(date)"
echo ""

echo "1️⃣ TESTING CONTENT EXISTENCE (WITHOUT PUBLISHED FILTER)"
echo "======================================================"
echo "🔍 Testing if content exists in database at all..."
curl -s "https://streamdb.online/api/content/content_1754261671794_3sztrufdx" | head -20
echo ""
echo ""

echo "2️⃣ TESTING CONTENT API WITH PUBLISHED FILTER"
echo "============================================="
echo "🔍 Testing content API with published=true filter..."
curl -s "https://streamdb.online/api/content?published=true&limit=5" | head -20
echo ""
echo ""

echo "3️⃣ CHECKING IF UPDATED SERVER CODE IS DEPLOYED"
echo "=============================================="
echo "🔍 Making a test request to see which query is running..."
echo "📋 If you see 'AND is_published = 1' in server logs, the fix is deployed"
echo "📋 If you see logs without the filter, the file wasn't uploaded"

# Make request to trigger debug endpoint
curl -s "https://streamdb.online/api/debug/meta/content_1754261671794_3sztrufdx" > /dev/null

echo "📊 Latest server logs (should show database query):"
pm2 logs streamdb-online --lines 5
echo ""

echo "4️⃣ TESTING ALTERNATIVE CONTENT IDs"
echo "================================="
echo "🔍 Testing with different content IDs from the API response..."
echo "📋 Testing with: content_1755547321661_rgeqe1diz"
curl -s "https://streamdb.online/api/debug/meta/content_1755547321661_rgeqe1diz" | head -10
echo ""
echo "📋 Testing with: content_1755307803599_57ve9lzkw"
curl -s "https://streamdb.online/api/debug/meta/content_1755307803599_57ve9lzkw" | head -10
echo ""

echo "5️⃣ FINAL DIAGNOSIS"
echo "=================="
echo "🔍 Based on the results above:"
echo ""
echo "📊 If Step 1 returns content data:"
echo "   ✅ Content exists in database"
echo "   ❌ Issue: Content not published OR server not updated"
echo ""
echo "📊 If Step 1 returns 404:"
echo "   ❌ Content doesn't exist in database"
echo "   🚨 Need to use different content ID"
echo ""
echo "📊 If Step 3 shows 'AND is_published = 1' in logs:"
echo "   ✅ Updated server code is deployed"
echo "   ❌ Issue: Content not published in database"
echo ""
echo "📊 If Step 3 shows logs without the filter:"
echo "   ❌ Updated server code NOT deployed"
echo "   🚨 Need to upload server/index.js to production"
echo ""
echo "📊 If Step 4 works with other content IDs:"
echo "   ✅ System is working"
echo "   ❌ Issue: Wrong content ID being tested"

echo ""
echo "✅ DIAGNOSIS COMPLETED"
echo "====================="