# 🛠️ SAFE MANUAL EDIT APPROACH - GU<PERSON>ANTEED TO WORK

## 🚨 **ISSUE IDENTIFIED**

The `-c` flag expects a complete nginx configuration. Let's use the SAFEST approach - manual editing of your working config.

## ✅ **GUARANTEED SAFE METHOD**

### **Step 1: Create Backup**
```bash
sudo cp /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-available/streamdb.online.backup.$(date +%Y%m%d_%H%M%S)
```

### **Step 2: Edit Your Working Config**
```bash
sudo nano /etc/nginx/sites-available/streamdb.online
```

### **Step 3: Make These EXACT Changes**

#### **A. Find this section (around line 25-30):**
```nginx
# Main website - HTTP to HTTPS redirect
server {
    listen 80;
    server_name streamdb.online www.streamdb.online;
    
    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Redirect all HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}
```

#### **B. Replace it with:**
```nginx
# Redirect www to non-www (HTTP)
server {
    listen 80;
    server_name www.streamdb.online;
    
    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    return 301 https://streamdb.online$request_uri;
}

# Redirect non-www HTTP to HTTPS
server {
    listen 80;
    server_name streamdb.online;
    
    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    return 301 https://streamdb.online$request_uri;
}
```

#### **C. Find this line (around line 40-50):**
```nginx
server_name streamdb.online www.streamdb.online;
```

#### **D. Change it to:**
```nginx
server_name streamdb.online;
```

#### **E. Add this server block BEFORE the main HTTPS server block:**
```nginx
# Redirect www to non-www (HTTPS)
server {
    listen 443 ssl http2;
    server_name www.streamdb.online;
    
    # SSL Configuration (Cloudflare handles certificates)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    return 301 https://streamdb.online$request_uri;
}
```

### **Step 4: Test and Apply**
```bash
# Test the configuration
sudo nginx -t

# If test passes, apply the changes
sudo systemctl reload nginx
```

### **Step 5: Verify**
```bash
# Test www redirect
curl -I https://www.streamdb.online/
# Should return: HTTP/1.1 301 Moved Permanently
# Location: https://streamdb.online/

# Test main site still works
curl -I https://streamdb.online/
# Should return: HTTP/1.1 200 OK
```

This approach is 100% safe because it only modifies your existing working configuration with minimal changes.